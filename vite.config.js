import { defineConfig } from "vite";
import path from "path";
import uni from "@dcloudio/vite-plugin-uni";
import remToRpx from "postcss-rem-to-responsive-pixel";

export default defineConfig({
  plugins: [uni()],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/style/main.scss";`, // 根据实际路径调整
      },
    },
    postcss: {
      plugins: [
        remToRpx({
          // 核心配置项
          rootValue: 75,          // 1rem 对应的像素值（根据原项目设计稿调整）
          transformUnit: "rpx",    // 目标单位
          propList: ["*"],         // 转换所有属性的 rem 单位
          mediaQuery: false,       // 不处理媒体查询中的 rem
          minRemValue: 0.02,          // 小于 1rem 不转换
        })
      ]
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./"), // 确保别名已定义
    },
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      '/apis': {
        target: 'http://b118.oelove.com/',
        // target: 'https://e.oephp.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/apis/, ''),
        followRedirects: true,
      },
      '/upload': {
        target: 'http://b118.oelove.com/',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/apis/, ''),
        followRedirects: true,
      },
    },
  },
});