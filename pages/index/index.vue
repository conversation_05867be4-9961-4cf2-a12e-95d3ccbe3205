<template>
  <CustomNavBar title="红娘工作台" />

  <SkeletonScreen v-if="loading" />
  <scroll-view v-else scroll-y style="height: calc(100vh - 92rpx)" @scroll="handleScroll">
    <view class="home">
      <!-- 入口菜单 -->
      <MenuGrid />

      <!-- 筛选 -->
      <template v-if="showFilter">
        <view class="filter_row filter_box" :class="{ scrolled: stickyTop === 0 }">
          <view class="filter_item" @click="openRangePopup">
            <text class="last">{{ viewTypeText }}</text>
            <text class="iconfont icon-zhankai-011"></text>
          </view>
          <view class="filter_item date_filter">
            <DateRangeSelect v-model="day" @change="changeDay" />
            <view class="time_select">
              <text class="first">{{ dayText }}</text>
              <text class="iconfont icon-rili"></text>
            </view>
          </view>
        </view>
      </template>

      <view class="data_box">
        <!-- 业绩 -->
        <view class="chart_row" v-if="showStoreData || showMyData">
          <view class="performance">
            <LiquidFillChart :value="financeData.complete" />
          </view>
          <GoalBlock :finance-data="financeData" />
        </view>

        <!-- 门店、部门数据 -->
        <DataStatistics
          type="store"
          :viewTypeText="viewTypeText"
          :dayText="dayText"
          :data="storeData"
          :hasSale="hasSale"
          :hasServ="hasServ"
          :hasWorkPower="hasWorkPower"
          :show="showStoreData"
        />

        <!-- 本人数据 -->
        <DataStatistics type="my" :viewTypeText="viewTypeText" :dayText="dayText" :data="myData" :hasSale="hasSale" :hasServ="hasServ" :show="showMyData" />

        <!-- 漏斗 -->
        <template v-if="showFunnel">
          <view class="funnel_row" :style="{ paddingTop: !showFilter && '32rpx' }">
            <view class="filter_row" style="padding: 0; height: auto; margin-bottom: 16rpx">
              <view class="type_filter">
                <!-- 只有同时有销售和服务权限时才显示切换选项 -->
                <uni-section :style="{ width: '280rpx' }" v-if="hasFunnel1Power && hasFunnel2Power">
                  <uni-data-select placeholder="" :localdata="funnelStatus" v-model="funnelType"></uni-data-select>
                </uni-section>

                <view class="time_select type_select">
                  <text class="type">
                    {{ funnelType === FUNNEL_TYPES.SALES ? '销售漏斗' : '服务漏斗' }}
                  </text>
                  <text class="date">[{{ dayText }}]</text>
                  <text v-if="hasFunnel1Power && hasFunnel2Power" class="iconfont icon-zhankai-011"></text>
                </view>
              </view>

              <view class="filter_item date_filter" v-if="!showFilter">
                <DateRangeSelect v-model="day" @change="changeDay" />
                <view class="time_select">
                  <text class="first">{{ dayText }}</text>
                  <text class="iconfont icon-rili"></text>
                </view>
              </view>
            </view>
            <SalesFunnelChart :value="funnelData" />
          </view>
        </template>
      </view>

      <PopupContainer ref="rangePopup" title="数据范围" showClose closeText="重置" @confirm="handleConfirmRange" @reset="handleResetRange">
        <template #content>
          <view class="range_popup">
            <view class="range_type">
              <view class="range_type_item" :class="getRangeItemClass(item)" v-for="item in viewTypes" :key="item.value" @click="handleViewType(item)">
                {{ item.text }}
              </view>
            </view>
            <TreeView v-model="rangeForm.s_roleid" :tree="netpicker.depart" :disabled="treeDisabled" title="选择部门" @confirm="handleDepartment" />
            <Tree v-model="rangeForm.s_makerid" :tree="netpicker.maker" :disabled="treeDisabled" isShowRow title="选择红娘" />
          </view>
        </template>
      </PopupContainer>
    </view>
  </scroll-view>
  <CustomTabBar :current="0" />
</template>

<script setup>
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { useStore } from 'vuex';
  import { usePermission } from '@/composables/usePermission';

  import PopupContainer from '@/oeui/new/PopupContainer';
  import Tree from '@/oeui/new/Tree/Tree';
  import TreeView from '@/oeui/new/TreeView/TreeView';
  import LiquidFillChart from '@/oeui/new/Chart/LiquidFillChart';
  import SalesFunnelChart from '@/oeui/new/Chart/SalesFunnelChart';
  import DateRangeSelect from '@/oeui/new/DateRangeSelect';
  import CustomTabBar from '@/oeui/new/CustomTabBar';
  import MenuGrid from '@/oeui/new/MenuGrid';
  import DataStatistics from '@/oeui/new/DataStatistics';
  import GoalBlock from '@/oeui/new/GoalBlock/GoalList';
  import SkeletonScreen from '@/oeui/new/SkeletonScreen';

  import { getClassAll, getCalssMaker } from '@/api/common.js';
  import { getMyWorkTotal, getWorkTotal, getSalefunnel, getServefunnel } from '@/api/total.js';
  import { dayArray, FUNNEL_TYPES, funnelStatus } from '@/assets/staticData';

  const allViewTypes = Object.freeze([
    { text: '本人数据', value: '1' },
    { text: '部门数据', value: '2' },
    { text: '门店数据', value: '3' }
  ]);

  const store = useStore();
  const { hasPermission, hasModsPermission, getSafeJson } = usePermission();

  // 资源权限
  const hasWorkPower = hasPermission('work', 'work_view');
  const hasMyWorkPower = hasPermission('mywork', 'mywork_view');
  const hasFunnel1Power = hasPermission('funnel1', 'funnel1_view');
  const hasFunnel2Power = hasPermission('funnel2', 'funnel2_view');
  const hasSale = hasModsPermission(['menu_sale']);
  const hasServ = hasModsPermission(['menu_serv']);

  // 搜索参数
  const search = reactive({
    s_date1: '',
    s_date2: '',
    s_time: '',
    s_roleid: '',
    s_makerid: '',
    viewtype: store.state?.loginInfo?.pwscope,
    s_refresh: ''
  });

  const originalSearch = {
    s_roleid: '',
    s_makerid: '',
    viewtype: store.state?.loginInfo?.pwscope
  };

  // 根据权限更新viewtype
  const correctViewTypes = () => {
    const vuexData = getSafeJson(uni.getStorageSync('vuex') || {});
    const pwscope = store.state?.loginInfo?.pwscope || vuexData?.loginInfo?.pwscope;
    if ((pwscope === '2' || pwscope === '3') && !hasWorkPower) {
      search.viewtype = '1';
      originalSearch.viewtype = '1';
    }
  };

  // 状态
  const stickyTop = ref(null);
  const rangePopup = ref(null);
  const rankType = ref('sale');
  const loading = ref(false);

  // 数据管理
  const netpicker = reactive({
    depart: [],
    maker: []
  });

  const rangeForm = reactive({
    s_roleid: '',
    s_makerid: '',
    viewtype: search.viewtype
  });

  const financeData = reactive({
    yeji_amount: 0,
    saletarget: 0,
    complete: 0
  });

  const storeData = reactive({
    member_quota: 0,
    waitallot_quota: 0,
    sign_quota: 0,
    sign_percent: 0,
    sea_quota: 0,
    fail_quota: 0,
    serv_member_quota: 0,
    finish_quota: 0,
    find_quota: 0,
    find_percent: 0
  });

  const myData = reactive({
    member_quota: 0,
    new_quota: 0,
    sign_quota: 0,
    sign_percent: 0,
    sea_quota: 0,
    inter_signup_quota: 0,
    serv_member_quota: 0,
    finish_quota: 0,
    find_quota: 0,
    find_percent: 0
  });

  // 漏斗类型切换处理
  const funnelData = ref([]);
  const funnelType = ref(hasFunnel1Power && hasFunnel2Power ? FUNNEL_TYPES.SALES : hasFunnel1Power ? FUNNEL_TYPES.SALES : FUNNEL_TYPES.SERVICE);

  const day = ref('');
  const dayText = computed(() => {
    const item = dayArray.find(item => item.value === day.value);
    return item ? item.text : '';
  });

  const viewTypes = computed(() => {
    const { pwscope } = store.state.loginInfo;
    return allViewTypes.filter(item => {
      if (pwscope === '3') return true;
      if (pwscope === '2') return Number(item.value) <= 2;
      if (pwscope === '1') return item.value === '1';
      return false;
    });
  });

  const getRangeItemClass = computed(() => {
    return item => ({
      active: rangeForm.viewtype === item.value,
      disabled: (item.value === '1' && !hasMyWorkPower) || (['2', '3'].includes(item.value) && !hasWorkPower)
    });
  });

  const viewTypeText = computed(() => {
    const map = { 1: '我的', 2: '部门', 3: '门店' };
    return map[search.viewtype] || '';
  });

  const treeDisabled = computed(() => rangeForm.viewtype === '1');

  const showStoreData = computed(() => ['门店', '部门'].includes(viewTypeText.value) && hasWorkPower);

  const showMyData = computed(() => viewTypeText.value === '我的' && hasMyWorkPower);

  const showFunnel = computed(() => hasFunnel1Power || hasFunnel2Power);

  const showFilter = computed(() => hasWorkPower || hasMyWorkPower);

  // 监听快捷时间筛选
  const changeDay = ({ timeArray } = {}) => {
    Object.assign(search, {
      s_date1: timeArray?.[0] || '',
      s_date2: timeArray?.[1] || ''
    });
    return onSearch();
  };

  // 监听数据范围类型
  const handleViewType = item => {
    if (item.value === '1' && !hasMyWorkPower) return;
    if (['2', '3'].includes(item.value) && !hasWorkPower) return;

    Object.assign(rangeForm, {
      viewtype: item.value,
      s_roleid: '',
      s_makerid: ''
    });

    if (item.value !== '1') {
      getClassData(item.value);
      getMakerData(item.value);
    }
  };

  // 显示数据范围弹窗
  const openRangePopup = () => {
    const { s_roleid, s_makerid, viewtype } = search;
    Object.assign(rangeForm, { s_roleid, s_makerid, viewtype });

    if (viewtype !== '1') {
      Promise.all([getClassData(), getMakerData()]).then(() => rangePopup.value?.open());
    } else {
      rangePopup.value?.open();
    }
  };

  // 重置数据范围参数
  const handleResetRange = () => {
    rangePopup.value?.close();
    Object.assign(search, { ...originalSearch });
    Object.assign(rangeForm, {
      s_roleid: '',
      s_makerid: '',
      viewtype: originalSearch.viewtype
    });
    onSearch();
  };

  // 确认数据范围
  const handleConfirmRange = () => {
    rangePopup.value?.close();
    const { s_roleid, s_makerid, viewtype } = rangeForm;
    Object.assign(search, { s_roleid, s_makerid, viewtype });
    onSearch();
  };

  // 监听部门选择
  const handleDepartment = node => {
    Object.assign(rangeForm, { s_roleid: node.roleid, s_makerid: '' });
    getMakerData(rangeForm.viewtype);
  };

  // 获取部门数据
  const getClassData = async type => {
    const viewtype = type || rangeForm.viewtype;
    const a = viewtype === '2' ? 'mydepart' : 'sitedepart';
    const res = await getClassAll({ viewtype, a });
    if (res.ret === 1) {
      netpicker.depart = res.result.data.map(item => ({ first: 1, ...item }));
    }
  };

  // 获取运营人员数据
  const getMakerData = async type => {
    const viewtype = type || rangeForm.viewtype;
    const res = await getCalssMaker({ viewtype, s_roleid: rangeForm.s_roleid });
    if (res.ret === 1) {
      netpicker.maker = res.result.data;
    }
  };

  // 获取销售、服务资源数据
  const fetchMemberTotal = async (isMy = false, role) => {
    if (!showFilter.value) return;

    try {
      uni.showLoading();
      const a = `${role === 'serv' ? 'serv' : 'member'}${isMy && role === 'serv' ? 'member' : ''}`;
      const res = await (isMy ? getMyWorkTotal : getWorkTotal)({ a, ...search });
      if (res.ret !== 1) return;

      // 处理数据
      const target = isMy ? myData : storeData;
      const data = { ...res.result.data, ...(role === 'serv' && { serv_member_quota: res.result.data.member_quota }) };
      const keys = {
        my: {
          sale: ['member_quota', 'new_quota', 'sign_quota', 'sign_percent', 'sea_quota', 'inter_signup_quota'],
          serv: ['serv_member_quota', 'finish_quota', 'find_quota', 'find_percent']
        },
        store: {
          sale: ['member_quota', 'waitallot_quota', 'sea_quota', 'sign_percent', 'fail_quota'],
          serv: ['serv_member_quota', 'finish_quota', 'find_quota', 'find_percent']
        }
      }[isMy ? 'my' : 'store'][role];
      keys.forEach(k => (target[k] = +(data[k] || 0)));
    } catch (error) {
      console.error(error);
    } finally {
      uni.hideLoading();
    }
  };

  // 获取业绩数据
  const fetchFinanceData = async (isMy = false) => {
    if (!showFilter.value) return;
    const a = isMy ? (rankType.value === 'serv' ? 'servfinance' : 'finance') : 'finance';
    const api = isMy ? getMyWorkTotal : getWorkTotal;
    const res = await api({ a, ...search });
    if (res.ret === 1) {
      Object.assign(financeData, res.result.data);
    }
  };

  // 获取漏斗数据
  const getFunnelTotal = async () => {
    if (!showFunnel.value) return;

    const apiMap = {
      [FUNNEL_TYPES.SALES]: getSalefunnel,
      [FUNNEL_TYPES.SERVICE]: getServefunnel
    };
    const selectedApi = apiMap[funnelType.value];
    const hasPermission = (funnelType.value === FUNNEL_TYPES.SALES && hasFunnel1Power) || (funnelType.value === FUNNEL_TYPES.SERVICE && hasFunnel2Power);

    if (!hasPermission || !selectedApi) return;

    try {
      const res = await selectedApi(search);
      if (res?.ret === 1) {
        funnelData.value = res.result.funnel.data.map(item => ({
          name: item.catname,
          value: Number(item.member_quota),
          stepid: item.stepid
        }));
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onSearch = () => {
    const isMy = search.viewtype === '1';

    // 销售、服务数据请求
    if (hasWorkPower || hasSale || (!hasSale && !hasServ)) {
      fetchMemberTotal(isMy, 'sale');
    }
    if (hasServ) {
      fetchMemberTotal(isMy, 'serv');
    }

    fetchFinanceData(isMy);
    getFunnelTotal();
  };

  // 监听页面滚动事件
  let lastScrollTime = 0;
  const handleScroll = e => {
    const now = Date.now();
    if (now - lastScrollTime > 50) {
      requestAnimationFrame(() => {
        getStickyPosition();
        lastScrollTime = now;
      });
    }
  };

  // 处理吸顶判断
  const getStickyPosition = () => {
    const query = uni.createSelectorQuery().in(this);
    query
      .select('.filter_box')
      .boundingClientRect(rect => {
        if (rect) stickyTop.value = rect.top;
      })
      .exec();
  };

  onMounted(() => {
    correctViewTypes();
    onSearch();
  });

  watch(funnelType, () => {
    if (hasFunnel1Power && hasFunnel2Power) {
      getFunnelTotal();
    }
  });
</script>

<style lang="scss" scoped>
  .home {
    @include flex-column;
    width: 100%;
    padding-bottom: 100rpx;

    .data_box {
      padding: 0 32rpx;
      background-color: #fff;

      .title_bold {
        font-size: 32rpx;
        color: #000;
        font-weight: bold;
        margin-bottom: 48rpx;
      }

      .funnel_row {
        padding: 64rpx 0;

        .type_filter {
          @include flex-center;
          position: relative;

          .type_select {
            width: 280rpx;
            @include flex-center;
            justify-content: flex-start;
            position: absolute;
            left: 0;
            z-index: 1;
            color: #000;

            .iconfont {
              font-size: 42rpx;
            }

            .type {
              font-size: 32rpx;
              font-weight: bold;
              margin-right: 8rpx;
            }

            .date {
              font-size: 28rpx;
              color: #666;
              margin-right: 4rpx;
            }
          }
        }
      }

      .chart_row {
        @include flex-between;
        padding: 64rpx 0;
        border-bottom: 1rpx solid #f7f7f9;

        .performance {
          width: 240rpx;
          height: 240rpx;
          padding-right: 32rpx;
          margin-left: 32rpx;
        }
      }
    }

    .filter_row {
      @include flex-between;
      padding: 0 32rpx;
      background: #fff;
      height: 92rpx;
      position: sticky;
      position: -webkit-sticky;
      top: 0;
      z-index: 88;
      transition: box-shadow 0.1s ease;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0);

      &.scrolled {
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      }

      .filter_item {
        @include flex-center;
        color: #000;
        position: relative;

        .time_select {
          @include flex-center;
          position: absolute;
          right: 0;
          z-index: 1;
        }

        .last {
          font-weight: 550;
          font-size: 40rpx;
        }

        .first {
          font-size: 32rpx;
        }

        .iconfont {
          font-size: 42rpx;
        }

        .icon-rili {
          font-size: 32rpx;
          margin-left: 6rpx;
        }
      }
    }

    .range_popup {
      padding: 16rpx 0;

      .range_type {
        @include flex-wrap;
        margin-bottom: 20rpx;

        &_item {
          width: 168rpx;
          height: 64rpx;
          @include flex-center;
          font-size: 28rpx;
          color: #000;
          cursor: pointer;
          background: #f7f7f9;
          box-sizing: border-box;
          border-radius: 16rpx;
          margin-right: 24rpx;

          &.active {
            background: #6365e0;
            color: #fff;
          }

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }

      .range_option {
        @include flex-between;
        height: 84rpx;
        margin-bottom: 8rpx;

        &.disabled {
          opacity: 0.5;
        }

        &_title {
          width: 160rpx;
          font-size: 32rpx;
          color: #000;
        }

        &_content {
          @include flex-align-center;
          justify-content: flex-end;
          flex: 1;
          height: 100%;

          text {
            color: #c0c4cc;
            margin-right: 12rpx;
            font-size: 32rpx;
          }
        }
      }
    }
  }
</style>
