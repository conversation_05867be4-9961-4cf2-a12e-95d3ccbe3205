<template>
  <view class="top_box">
    <image src="@/assets/images/login_bg.png" />
  </view>
  <view class="oe_login">
    <view class="flex_dc" style="height: 310rpx">
      <image style="width: 284rpx; height: 140rpx" src="@/assets/images/title.png" alt="" />
    </view>
    <view class="content_box">
      <view class="title">账号登陆</view>
      <view class="oe_input">
        <input type="text" placeholder="请输入手机号或用户名" maxlength="11" v-model="state.mobile" />
        <span class="iconfont icon-guanbi" v-if="state.mobile" @click="clearInput('mobile')"></span>
      </view>
      <view class="oe_input">
        <input style="padding-top: 0.2133rem" type="password" placeholder="请输入密码" v-model="state.password" v-if="!inputStatus" />
        <input type="text" placeholder="请输入密码" v-model="state.password" v-else />
        <span class="oeuifont oeui-yanjing" :class="inputStatus ? 'current' : ''" v-if="state.password" @click="toggleInput"></span>
      </view>
      <view class="btn_box">
        <view class="btn" @click="sendLogin" :class="state.mobile && state.password ? 'current' : ''">登录</view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref, onMounted } from 'vue';
  import { useStore } from 'vuex';

  import { loginPassword } from '@/api/login.js';
  const { proxy } = getCurrentInstance();

  const store = useStore();
  const state = reactive({
    mobile: '',
    password: ''
  });
  const inputStatus = ref(false);

  onMounted(() => {
    if (uni.getStorageSync('maker_sign')) {
      uni.switchTab({ url: '/pages/index/index' });
    }
  });

  const clearInput = name => {
    //清除input值
    state[name] = '';
  };
  const toggleInput = () => {
    //切换密码显示
    inputStatus.value = !inputStatus.value;
  };

  const sendLogin = () => {
    if (state.mobile == '') {
      uni.showToast({
        title: '请填写手机号或用户名',
        icon: 'none'
      });

      return;
    }
    if (state.password == '') {
      uni.showToast({
        title: '请填写密码',
        icon: 'none'
      });

      return;
    }
    uni.showLoading({ title: '登录中...' });
    loginPassword({
      type: 'h5',
      loginname: state.mobile,
      password: state.password
    }).then(res => {
      uni.hideLoading();
      if (res.ret == 1) {
        if (res.result.info.is_operate != 1) {
          uni.redirectTo({ url: '/pages/reg/error' });
          return;
        }
        store.dispatch('setUserInfo', res.result);
        uni.switchTab({ url: '/pages/index/index' });

        setTimeout(() => {
          state.mobile = '';
          state.password = '';
        }, 1000);
      } else {
        uni.showToast({
          title: res.msg || '登录失败，请检查',
          icon: 'none'
        });
      }
    });
  };
</script>

<style lang="scss" scoped>
  @import url('~@/oeui/icon/iconfont.css');
  @import url('~@/assets/iconfont/iconfont.css');

  .top_box {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 7rem;
    width: 100%;
    image {
      width: 100%;
    }
  }

  .oe_login {
    position: relative;
    min-height: 100%;
    box-sizing: border-box;
    .back {
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      position: absolute;
      width: 1.3333rem;
      height: 1.3333rem;
      color: #fff;
      left: 0;
      i {
        font-size: 0.5333rem;
      }
    }

    .content_box {
      padding: 0 0.64rem;
      height: calc(100vh - 4.1333rem);
      position: relative;
      z-index: 1;
      background: #fff;
      border-radius: 28px 28px 0px 0px;
    }

    .title {
      font-size: 0.8533rem;
      padding-top: 0.7467rem;
      color: $color_main;
      line-height: 1.0133rem;
      font-weight: 600;
    }
    .tips {
      font-family: PingFang SC, PingFang SC;
      margin-top: 0.4rem;
      color: #666;
      font-weight: normal;
      font-size: 0.3467rem;
      margin-bottom: 0.9067rem;
    }

    .oe_input {
      background: #f9f9fe;
      position: relative;
      height: 1.28rem;
      margin-top: 0.4267rem;
      border-radius: 0.64rem;
      display: flex;
      align-items: center;

      input {
        width: 100%;
        height: 1.0667rem;
        box-sizing: border-box;
        padding: 0 0.5333rem;
        font-size: 0.4267rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: normal;

        &::placeholder {
          color: #c3c3c5;
          font-size: 0.3733rem;
        }
      }

      .oeuifont {
        color: #c9cdd4;
        position: absolute;
        right: 0;
        padding-left: 0.267rem;
        padding-right: 0.4rem;
        font-size: 0.56rem;
        line-height: 1.333rem;
        cursor: pointer;

        &.oeui-yanjing {

          &.current {
            color: $color_main;
          }
        }
      }
      .iconfont {
        color: #c9cdd4;
        position: absolute;
        right: 0;
        padding-left: 0.267rem;
        padding-right: 0.4rem;
        line-height: 1.333rem;
        cursor: pointer;
        &.current {
          color: $color_main;
        }
      }

      .getcode {
        position: absolute;
        color: $color_main;
        font-size: 0.4267rem;
        cursor: pointer;
        right: 0.5333rem;

        &.current {
          color: #999999;
        }
      }
    }

    .btn_box {
      margin-top: 0.8533rem;

      .btn {
        width: 100%;
        text-align: center;
        background-color: $color_main;
        font-size: 0.427rem;
        line-height: 0.5867rem;
        border-radius: 1rem;
        color: #ffffff;
        padding: 0.2933rem 0;
        box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        cursor: pointer;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: normal;
        opacity: 0.5;

        &.current {
          opacity: 1;
        }
      }
    }
  }
  .bg_f9 {
    background: #f9f9fe;
  }
</style>
