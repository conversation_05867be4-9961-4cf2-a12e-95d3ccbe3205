<template>
  <div class="errpage">
    <img class="errico" src="@/assets/images/error.png" alt="" />
    <span class="fz18 color_font1 fb" style="margin: 16rpx 0">温馨提示</span>
    <p>你不是运营人员，红娘请在PC端登录</p>
    <span class="submitbtn" @click="goLogin">重新登录</span>
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue';
  const goLogin = () => {
    uni.redirectTo({
      url: '/pages/reg/login'
    });
  };
</script>
<style lang="scss" scoped>
  .errpage {
    height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    background-color: #fff;
  }
  .errico {
    height: 300rpx;
    width: 300rpx;
    margin-top: 200rpx;
  }
  .submitbtn {
    margin-top: 60rpx;
    width: 80%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 999px;
    font-size: 32rpx;
    background-color: $color_main;
    color: #fff;
  }
</style>
