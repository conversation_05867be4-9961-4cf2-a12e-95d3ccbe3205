<template>
  <CustomNavBar title="到店预测" showLeft showRight @rightClick="showFilterDrawer" />
  <DropDownMenu @confirm="handleConfirm">
    <DropDownItem title="签约状态" :options="ordsignOptions" v-model="search.s_ordsign"></DropDownItem>
    <DropDownItem title="到店状态" :options="status_s" v-model="search.s_flag"></DropDownItem>
    <DropDownItem title="回访状态" :options="followflagOptions" v-model="search.s_followflag"></DropDownItem>
  </DropDownMenu>
  <ScrollView
    class="allot_scroll"
    :list="list"
    :refreshing="refreshing"
    :loading="loading"
    :hasMore="hasMore"
    emptyText="暂无到店预测资源"
    @refresh="onRefresh"
    @loadMore="loadMore"
  >
    <view class="resource_box p12">
      <view class="resource_list">
        <view class="resource_list_item plr16 br12 mt12 bg_f" v-for="(item, i) in list" :key="item.interid">
          <view class="top bo_bottom flex_dc flex_jsb">
            <view class="color_6">
              邀约单号&nbsp;
              <text>{{ item.interno }}{{ item.mid }}</text>
            </view>
            <view class="top_r">至尊服务套餐</view>
          </view>
          <view class="content mb12">
            <view class="top flex_dc flex_jsb">
              <view class="top_l flex_dc">
                <SafeImage :src="item.member.headimg_url" class="img24 br8 mr8" />
                <text class="font-w">{{ item.member?.nickname }}</text>
              </view>
              <view class="top_r">
                <view v-if="item.flag == 1" class="btn_green{">已到店</view>
                <view v-else-if="item.flag == 2" class="btn_info">爽约</view>
                <view v-else-if="item.flag == 3" class="btn_red">取消</view>
                <view v-else-if="item.flag == 0" class="btn_warn">待到店</view>
              </view>
            </view>
            <view class="content_color_6 mb12">
              资源ID:{{ item.member?.mid }}
              <span v-if="item.member?.userid > 0">&nbsp;/&nbsp;线上ID:{{ item.member?.userid }}</span>
            </view>
            <MobileView class="mb12" :mobile="item.member?.mobile" :id="item.mid" :info="item.member?.mobile_info" />
            <view class="mb12 font-s fz16">邀约人：{{ item.inter_maker?.name || '--' }}</view>
            <view class="mb12 font-s fz16 flex">
              <view class="w50">
                回访：
                <text v-if="item.followflag == 1" class="color_success">已回访</text>
                <template v-else>
                  <text v-if="item.flag == 1 || item.flag == 2">工单结束(待回访)</text>
                  <text v-else>待回访</text>
                </template>
              </view>
              <view class="w50">
                签约：
                <span v-if="item.ordsign == 1">已签约</span>
                <span v-else>未签约</span>
              </view>
            </view>
            <view class="font-s fz16">
              预约到店：
              <span class="color_title2">{{ getTime(item.intertime, false, 'text', '/') || '--' }}</span>
            </view>
          </view>
          <view class="footer_row">
            <view class="operate" v-if="item.flag == 0 && item.followflag == 0 && hasPowerPermission(['followup_do'])" @click="followupClick(item)">
              <text>回访</text>
            </view>
            <view class="operate" v-if="item.flag == 0 && hasPowerPermission(['inter_edit'])" @click="followEdit(item)">
              <text>编辑</text>
            </view>
            <view class="operate">
              <text>跟进</text>
            </view>
            <view class="operate" v-if="item.flag == 0 && hasPowerPermission(['inter_cancel'])" @click="followCancel(item)">
              <text>取消</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </ScrollView>
  <InterAccess ref="accessInter" />
  <interCancel ref="cancelInter" />
  <interEdit ref="editInter" />
  <interFilter ref="filterInter" @search="onSearchFilter" :pickerViewOptions="timeOptions"/>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, getCurrentInstance } from 'vue';
import { getInterlist } from '@/api/sale';
import DropDownMenu from '@/oeui/new/DropDownMenu/DropDownMenu';
import ScrollView from '@/oeui/new/ScrollView';
import MobileView from '@/oeui/new/MobileView';
import SafeImage from '@/oeui/new/SafeImage.vue';
import DropDownItem from '@/oeui/new/DropDownMenu/DropDownItem.vue';
import { useStore } from 'vuex';
import { getTime } from '../../utils/utils';
import { interStatus_s } from '@/assets/staticData';
import InterAccess from './components/inter-access.vue';
import interCancel from './components/inter-cancel.vue';
import interFilter from './components/inter-filter.vue';
import interEdit from './components/inter-edit.vue';
import { usePermission } from '@/composables/usePermission';
const { hasPowerPermission } = usePermission();
const store = useStore();
const { proxy } = getCurrentInstance();
const ordsignOptions = ref([
  {
    text: '全部',
    value: ''
  },
  {
    text: '已签约',
    value: '1'
  },
  {
    text: '未签约',
    value: '2'
  }
]);
const status_s = [{ text: '全部', value: '' }].concat(interStatus_s);
const followflagOptions = ref([
  {
    text: '全部',
    value: ''
  },
  {
    text: '已回访',
    value: '1'
  },
  {
    text: '待回访',
    value: '2'
  }
]);
const timeOptions = ref([
  { text: '到店时间', value: 'intertime' },
  { text: '创建时间', value: 'addtime' }
]);
// 下拉筛选
const handleConfirm = () => {
  onSearch();
};
const pwscope = computed(() => store.state?.loginInfo.pwscope);
const list = ref([]);
const selectList = ref([]);
const refreshing = ref(false);
const loading = ref(false);
const hasMore = ref(true);
const state = reactive({
  page: 1,
  total: 0,
  pagesize: 20
});
const totalobj = reactive({
  total: 0,
  total_instore: 0,
  total_notsign: 0,
  total_hassign: 0
});
let search = reactive({
  viewtype: pwscope.value,
  s_roleid: '', //部门
  s_makerid: '', //红娘ID
  s_addmkid: '', //录入红娘
  s_facemkid: '', //面谈红娘
  s_helpmkid: '', //协助红娘
  s_mobile: '', //手机号
  s_name: '', //昵称姓名
  s_ordsign: '', //签约状态
  s_flag: '', // 到店状态
  s_followflag: '', //回访
  s_orderby: '', //排序
  s_mid: '', //资源ID
  s_timetype: 'intertime',
  s_time: '',
  s_date1: '',
  s_date2: ''
});

// 获取列表
const getList = type => {
  const p = getInterlist({
    ...search,
    page: state.page,
    pagesize: state.pagesize
  });

  p.then(res => {
    if (res.ret == 1) {
      if (type == 'more') {
        list.value = list.value.concat(res.result.data || []);
      } else {
        list.value = res.result.data || [];
      }
      state.total = Number(res.result.total);
      state.pagesize = res.result.pagesize;
      hasMore.value = list.value.length < Number(res.result.total);
      for (let key in totalobj) {
        if (res.result[key] != undefined) totalobj[key] = res.result[key];
      }
    }
  }).finally(() => {
    loading.value = false;
  });
  return p;
};
// 执行筛选搜索
const onSearchFilter = params => {
  Object.assign(search, params);
  onSearch();
};
// 执行搜索
const onSearch = () => {
  loading.value = true;
  getList();
};
onMounted(() => {
  onSearch();
});
const showFilterDrawer = () => {
  proxy.$refs.filterInter.open();
};
// 下拉刷新
const onRefresh = done => {
  state.page = 1;
  refreshing.value = true;
  getList().finally(() => {
    refreshing.value = false;
    done();
  });
};
//加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value && !refreshing.value) {
    state.page += 1;
    getList('more');
  }
};
// 回访弹框
const followupClick = info => {
  proxy.$refs.accessInter.openPopup(info);
};
// 编辑弹框
const followEdit = info => {
  proxy.$refs.editInter.openPopup(info);
};
// 取消弹框
const followCancel = info => {
  proxy.$refs.cancelInter.openPopup(info);
};
</script>

<style lang="scss" scoped>
.allot_scroll {
  @include flex-column;
  height: calc(100vh - 92rpx - 90rpx);
  width: 100%;
  background: #f7f7f9;

  .resource_box {
    padding: 0px 32rpx;

    .resource_list {
      &_item {
        .top {
          height: 100rpx;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: normal;
          &_r {
            color: #6365e0;
          }
        }
        .content {
          .font-w {
            font-family: PingFang SC;
            font-size: 18px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0px;
            /* 主标题色 */
            color: #000000;
          }
          .font-s {
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0px;

            font-variation-settings: 'opsz' auto;
            /* 主标题色 */
            color: #000000;
          }
          .content_color_6 {
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: normal;
            line-height: 16px;
            letter-spacing: 0px;
            font-variation-settings: 'opsz' auto;
            /* 副标色 */
            color: #666666;

            z-index: 1;
          }
        }
        .footer_row {
          height: 100rpx;
          border-top: 1px solid #f7f7f9;
          @include flex-center;
          @include flex-between;

          .operate {
            position: relative;
            @include flex-center;
            justify-content: center;
            flex: 1;
            color: #6365e0;
            font-size: 32rpx;
            line-height: 1;

            &:after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 2rpx;
              height: 24rpx;
              background-color: #dcdfe6;
            }
            &:last-child:after {
              content: '';
              width: 0rpx;
            }
            .icon-fenpei1,
            .icon-touhai {
              margin-right: 12rpx;
              line-height: 1;
            }
          }
        }
      }
    }
  }
}
</style>
