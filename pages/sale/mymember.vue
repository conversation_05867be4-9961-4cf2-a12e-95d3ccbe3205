<template>
  <CustomNavBar title="我的资源" showLeft showRight @rightClick="showFilterDrawer" />
  <DropDownMenu @confirm="handleDownSearch">
    <DropDownItem title="资源标记" :options="markOptions" v-model="search.s_mark"></DropDownItem>
    <DropDownItem title="跟进状态" :options="followOptions" v-model="search.s_follow"></DropDownItem>
    <DropDownItem v-if="prestepOptions.length" title="销售阶段" :options="prestepOptions" v-model="search.s_prestep"></DropDownItem>
  </DropDownMenu>

  <ScrollView class="allot_scroll" :list="list" :refreshing="refreshing" :loading="loading" :hasMore="hasMore" emptyText="暂无资源数据" @refresh="onRefresh" @loadMore="loadMore">
    <view v-if="list.length" class="resource_box">
      <view class="resource_list">
        <ResourceListItem v-for="item in list" :key="item.mid" :data="item" :isEdit="false" @select="handleSelect">
          <template #footer>
            <view class="footer_row">
              <div class="flex flex_ac">
                剩余坠海：
                <span :class="{ red: isWithinPast24Hours(item.predroptime) }">
                  {{ getDownDay(item.predroptime) || '--' }}
                </span>
              </div>
              <div class="follow_btn flex flex_dc">
                <span class="iconfont icon-bianji"></span>
                跟进
              </div>
            </view>
          </template>
        </ResourceListItem>
      </view>
    </view>
  </ScrollView>

  <view class="enter" @click="showMemberAddDrawer" v-power="'member_add'">
    <view class="small-btn confirm-small-btn">
      <text class="iconfont icon-jiahao"></text>
    </view>
  </view>

  <Tree ref="tree" title="选择红娘" @confirm="handleAllotConfirm" />

  <FilterDrawer ref="filter" @search="onSearch" />

  <MemberAdd ref="addMember" fromdb="allot" @search="onSearch" />

  <MemberEdit ref="editMember" @search="onSearch" />

  <CustomTabBar :current="1" />
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';

import CustomTabBar from '@/oeui/new/CustomTabBar';
import DropDownMenu from '@/oeui/new/DropDownMenu/DropDownMenu';
import DropDownItem from '@/oeui/new/DropDownMenu/DropDownItem.vue';

import ScrollView from '@/oeui/new/ScrollView';
import FilterDrawer from '@/components/Filter/FilterDrawer';
import ResourceListItem from '@/components/ResourceList/MemberItem';
import Tree from '@/components/TreeCascade/Tree';
import MemberAdd from '@/components/Member/Add';
import MemberEdit from '@/components/Member/Edit';
import { getSaleSaleUserList, getMakerChecked, postAllotPer } from '@/api/sale.js';
import { markStatus, followStatus } from '@/assets/staticData';
import { formatSeconds_t } from '@/utils/utils';
import { usePermission } from '@/composables/usePermission';
const { hasPowerPermission } = usePermission();

const store = useStore();
const pwscope = store.state?.loginInfo?.pwscope;

const { proxy } = getCurrentInstance();

const markOptions = ref(markStatus);
const followOptions = ref(followStatus);
const prestepOptions = ref([]);

const list = ref([]);
const selectList = ref([]);
const refreshing = ref(false);
const loading = ref(false);
const hasMore = ref(true);
const pageState = reactive({
  page: 1,
  total: 0,
  pagesize: 20
});

let search = reactive({
  viewtype: pwscope,
  s_roleid: '', //部门
  s_makerid: '', //红娘ID
  s_addmkid: '', //录入红娘
  s_facemkid: '', //面谈红娘
  s_helpmkid: '', //协助红娘
  s_fromcat: '', //来源
  s_mark: '', //重点资源
  s_follow: '', //跟进状态
  s_prestep: '', //销售阶段

  s_mobile: '', //手机号
  s_name: '', //昵称姓名
  s_snid: '', //导入批次
  s_wayid: '',
  s_actid: '',

  s_partyid: '',
  s_after: '', //红娘帮约
  s_tags: '', //标签
  s_remark: '', //备注
  s_national: '', //民族
  s_dist1: '',
  s_dist2: '',
  s_dist3: '',
  s_dist4: '',
  s_home1: '',
  s_home2: '',
  s_home3: '',
  s_home4: '',
  s_gender: '',
  s_age1: '',
  s_age2: '',
  s_education: '',
  s_salary: '',
  s_job: '',
  s_flag: '', // 有效状态
  s_binduid: '', //线上用户
  s_avatar: '', //头像

  s_orderby: '', //排序
  s_sign: '1',
  s_timetype: 'allottime',
  s_time: '',
  s_date1: '',
  s_date2: ''
});

const searchMore = reactive({
  fromcat: [],
  tags: [],
  marry: [],
  national: [],
  job: [],
  salary: [],
  education: []
});

// 分配
const quotaForm = reactive({
  id: '',
  ownerid: '',
  addquota: ''
});

const isAllSelected = computed(() => {
  return list.value.length > 0 && list.value.every(item => item.select);
});

const fetchData = async params => {
  try {
    loading.value = true;
    const res = await getSaleSaleUserList(params);
    if (res.ret === 1) {
      const newData = res.result.data || [];

      // 资源标记处理
      const newafter = res.result.newafter_member_quota;
      if (Number(newafter) > 0) {
        markOptions[5].text = `帮约${newafter}`;
      }

      return {
        list: newData.map(item => ({ ...item, select: false })),
        total: Number(res.result.total),
        pagesize: res.result.pagesize
      };
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const getDataList = async (reset = false) => {
  if (loading.value) return;

  if (reset) {
    pageState.page = 1;
    hasMore.value = true;
    list.value = [];
    selectList.value = [];
  }

  const params = { ...search, ...pageState, ...formArr() };
  const res = await fetchData(params);
  const { list: newList, total, pagesize } = res;
  if (newList) {
    list.value = reset ? newList : [...list.value, ...newList];
    pageState.total = total;
    pageState.pagesize = pagesize;
    hasMore.value = list.value.length < pageState.total;
    if (!reset) pageState.page += 1;
  }
};

// 获取剩余天数
const getDownDay = downtime => {
  let lasttime = downtime * 1000;
  let nowTime = new Date().getTime();
  let time = lasttime - nowTime;
  if (time < 1) {
    return '--';
  }
  return formatSeconds_t(parseInt(time / 1000), true);
};

const isWithinPast24Hours = timestamp => {
  const nowInSeconds = Math.floor(Date.now() / 1000);
  const diff = nowInSeconds - timestamp;
  return diff >= 0 && diff < 86400;
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value && !refreshing.value) {
    getDataList();
  }
};

// 下拉刷新
const onRefresh = done => {
  refreshing.value = true;
  getDataList(true).finally(() => {
    refreshing.value = false;
    done();
  });
};

// 显示投海弹窗
const setRelease = row => {
  const id = row === 'select' && selectList.value.length ? selectList.value.join(',') : row?.mid;
  if (!id) return;

  proxy.$showModal({
    title: '温馨提示',
    content: `确定将数据投海吗？`,
    success(res) {
      if (res.confirm) resourceRelease(id);
    }
  });
};

// 批量分配
const setAllotForSelect = () => {
  quotaForm.id = selectList.value.join(',');
  quotaForm.addquota = '';
  proxy.$refs.tree.open();
};

// 显示分配弹窗
const setAllot = row => {
  if (row === 'select' && selectList.value.length) {
    setAllotForSelect();
    return;
  }

  if (!row.mid) return;
  quotaForm.id = row.mid;
  quotaForm.addquota = '';
  proxy.$refs.tree.open();
};

// 检查配额
const checkQuota = (quotaForm, makerData) => {
  const remain_quota = makerData.can_remain || 0;
  const num = quotaForm.id.split(',').length;
  if (remain_quota === 0 || remain_quota < num) {
    return {
      currentNum: num,
      remain: remain_quota,
      makerName: makerData.lastname,
      salequota: makerData.salequota
    };
  }
  return null;
};

const handleAllotConfirm = async data => {
  if (!data?.makerid) {
    dispenseResource();
    return;
  }

  quotaForm.ownerid = data.makerid;
  const res = await getMakerChecked({ mkid: data.makerid });
  if (res.ret === 1) {
    const quota = checkQuota(quotaForm, res.result);
    if (!quota) {
      dispenseResource();
      return;
    }

    if (quota.remain > 0) {
      showConfirmationModal(quota);
    } else {
      showNoQuotaModal(quota);
    }
  }
};

// 配额不足
const showConfirmationModal = quota => {
  proxy.$showModal({
    title: '温馨提示',
    content: `你选择了${quota.currentNum}条分配资源，【${quota.makerName}】剩余配额为${quota.remain}条，是否继续分配？`,
    confirmText: '继续分配',
    cancelText: '放弃',
    success(res) {
      if (res.confirm) dispenseResource();
    }
  });
};

// 配额为0
const showNoQuotaModal = quota => {
  proxy.$showModal({
    title: '温馨提示',
    content: `【${quota.makerName}】剩余配额为0，需要修改配额请，联系店长修改`,
    confirmText: '知道了',
    cancelText: '',
    isColumn: true
  });
};

// 分配资源
const dispenseResource = async () => {
  try {
    const res = await postAllotPer(quotaForm);
    if (res.ret === 1) {
      uni.showToast({
        title: `已成功分配${res.result.ok_nums}条资源`,
        icon: 'none',
        image: '/assets/svg/success.svg'
      });
      getDataList(true);
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  }
};

// 资源投海
const resourceRelease = async id => {
  try {
    const res = await postAllotPer({ a: 'predrop', id });
    if (res.ret === 1) {
      uni.showToast({
        title: '操作成功',
        icon: 'none',
        image: '/assets/svg/success.svg'
      });
      selectList.value = [];
      removeListItem(id);
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  }
};

// 处理选中/取消选中
const handleSelect = item => {
  const index = list.value.findIndex(i => i.mid === item.mid);
  if (index !== -1) {
    // 切换选中状态
    list.value[index].select = !list.value[index].select;

    // 更新选中列表
    if (list.value[index].select) {
      selectList.value.push(item.mid);
    } else {
      const selectIndex = selectList.value.indexOf(item.mid);
      if (selectIndex !== -1) {
        selectList.value.splice(selectIndex, 1);
      }
    }
  }
};

// 全选/取消全选
const toggleSelectAll = selectAll => {
  list.value.forEach(item => {
    item.select = selectAll;
  });

  selectList.value = selectAll ? list.value.map(item => item.mid) : [];
};

// 根据id字符串删除选中的项
const removeListItem = idstr => {
  if (typeof idstr !== 'string') {
    return;
  }

  let idSet = new Set(
    idstr
      .split(',')
      .map(item => item.trim())
      .filter(item => item !== '')
  );

  if (idSet.size === 0) {
    return;
  }

  let uplist = [];
  for (let v = 0; v < list.value.length; v++) {
    const mid = list.value[v].mid;
    if (idSet.has(mid)) {
      if (pageState.total > 0) {
        pageState.total--;
      }
    } else {
      uplist.push(list.value[v]);
    }
  }

  list.value = uplist;
};

const formArr = () => {
  let data = {};
  for (let key in searchMore) {
    if (searchMore[key].length > 0) {
      data['s_' + key] = searchMore[key].join(',');
    }
  }
  return data;
};

const handleDownSearch = () => {
  onSearch(search);
};

const onSearch = params => {
  refreshing.value = false;
  search = params;
  getDataList(true);
};

const showFilterDrawer = () => {
  proxy.$refs.filter.open();
};

const showMemberAddDrawer = () => {
  proxy.$refs.addMember.open();
};

const showMemberEditDrawer = () => {
  proxy.$refs.editMember.open({ mid: '10273' });
};

onMounted(() => {
  if (uni.getStorageSync('crm_prestep')) {
    const presteps = JSON.parse(uni.getStorageSync('crm_prestep'));
    prestepOptions.value = [{ text: '全部', value: '' }, ...presteps];
  }

  getDataList();

  // showMemberAddDrawer();
  // showMemberEditDrawer();
});
</script>

<style lang="scss" scoped>
.enter {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  @include flex-center;
  z-index: 90;

  .small-btn {
    @include flex-center;
    width: 100rpx;
    height: 100rpx;
    background: #6365e0;
    border-radius: 999rpx;
    box-shadow: 0px 6px 20px 0px rgba(0, 3, 205, 0.32);

    .icon-jiahao {
      font-size: 60rpx;
      color: #fff;
    }

    &:active {
      background: #8284e6;
    }
  }
}

.allot_scroll {
  @include flex-column;
  height: calc(100vh - 92rpx - 92rpx);
  width: 100%;
  background: #f7f7f9;

  .resource_box {
    padding: 32rpx;

    .resource_list {
      .footer_row {
        @include flex-center;
        @include flex-between;
        color: #000;
        padding: 0 24rpx;

        .follow_btn {
          width: 168rpx;
          height: 72rpx;
          background-color: #6365e0;
          color: #fff;
          border-radius: 48rpx;
          font-size: 32rpx;

          .iconfont {
            margin-right: 8rpx;
          }
        }

        .red {
          color: #f53f3f;
        }
      }
    }
  }
}
</style>
