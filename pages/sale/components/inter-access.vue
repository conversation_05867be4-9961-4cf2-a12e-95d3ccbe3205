<template>
  <PopupContainer
    titlePosition="center"
    title="邀约回访"
    :showClose="true"
    :maskClick="true"
    ref="followupPopup"
    @confirm="handleConfirm"
  >
    <template #content>
      <view class="followup">
        <view class="followup_top flex_dc flex_jsb">
          <view class="font-s fz16">是否取消</view>
          <switch @change="switchChange" color="#6365e0"/>
        </view>
        <view class="font-info mb12">如客户回馈有特殊情况无法到店，请选择取消</view>
        <view class="followup_top" v-if="addForm.cancelflag == 1">
          <view class="font-s fz16 mb12">取消原因<text class="color_error">*</text></view>
          <EasyTextarea v-model="addForm.cancelcont" :isBackground="false" placeholder="请输入" class="mb12"/>
        </view>
        <view v-else>
          <timePicker
            @timeChanged="timeChanged"
            :initialTime="addForm.intertime_t"
          />
          <view class="font-info mb12">到店时间无变化则无须操作</view>
        </view>
        <view class="followup_top">
          <view class="font-s fz16 mb12">回访备注<text class="color_error"></text></view>
          <EasyTextarea v-model="addForm.followcont" :isBackground="false" placeholder="请输入" />
        </view>
      </view>
    </template>
  </PopupContainer>
</template>
<script setup>
import { ref, reactive, nextTick } from "vue";
import { postInterfollowup } from "@/api/sale.js";
import PopupContainer from "../../../oeui/new/PopupContainer.vue";
import EasyTextarea from '@/oeui/new/EasyTextarea';
import timePicker from "../../../oeui/new/picker/timePicker.vue";
import { getTime } from "@/utils/utils";
const followupPopup = ref(null);
const addForm = reactive({
  followflag: "1", //回访状态
  cancelflag: "0", //是否取消
  followcont: "", //回访备注
  intertime_t: "", //邀约时间
  cancelcont: "", //取消原因
});
const switchChange = (e) => {
  if (e.detail.value) {
    addForm.cancelflag = 1;
  } else {
    addForm.cancelflag = 0;
  }
};
// 打开向上弹框
const openPopup = async (data) => {
  if (data.interid) {
    addForm.id = data.interid;
  }
  if (data.intertime) {
    let time = new Date(parseInt(data.intertime) * 1000);
    addForm.intertime_t = time;
  }
  await nextTick();
  if (followupPopup.value && typeof followupPopup.value.open === "function") {
    followupPopup.value.open();
  }
};
const timeChanged = (e) => {
  addForm.intertime_t = e;
};
const handleConfirm = () => {
  if (addForm.cancelflag == 0 && !addForm.intertime_t) {
    uni.showToast({
      title: "请选择到店日期",
      icon: "none",
    });
    return;
  } else if (addForm.cancelflag == 1 && !addForm.cancelcont) {
    uni.showToast({
      title: "请输入取消原因",
      icon: "none",
    });
    return;
  }
  uni.showLoading(); // 显示加载状态
  postInterfollowup({ a: "savedo", ...addForm })
    .then(() => {
      uni.showToast({
        title: "操作成功",
        icon: 'success'
      });
      followupPopup.value.close();
    })
    .finally(() => uni.hideLoading());
};
defineExpose({ openPopup });
</script>
<style lang="scss" scoped>
.followup {
  .textarea {
    width: 100%;
    height: 200rpx;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    box-sizing: border-box;
    resize: none;
  }
  .font-s {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 主标题色 */
    color: #000000;
  }
}
</style>
