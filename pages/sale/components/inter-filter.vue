<template>
  <uni-drawer ref="drawer" mode="right" :mask="false" :mask-click="false">
    <view class="filter_content">
      <CustomNavBar title="筛选" :isBack='false' showLeft @back="back" />
      <scroll-view style="height: calc(100vh - 92rpx - 110rpx)" class="scroll-view-box" scroll-y>
        <view class="search_form_box">
          <view class="form_input_rows">
            <view class="form_picker_item">
              <view class="title">时间类型</view>
              <view class="picker_value">
                <view @click="showPickerView" class="value-container">
                  <text v-if="search.s_timetype" :class="{ value: search.s_timetype }" class="text text-ellipsis">{{ timetypeName }}</text>
                  <text v-else class="no">请选择</text>
                </view>
                <text class="iconfont icon-youjiantou1" :class="{ value: search.s_timetype }"></text>
              </view>
            </view>
            <view class="form_input_item">
              <datePicker type="range" title="自定义时间" :defaultDate="savedDate" @confirm="getDate" />
            </view>
            <view class="form_input_item">
              <view class="title">数据范围</view>
              <dataRange type="range" :defaultRange="savedRange" @confirm="getRange" />
            </view>
            <view class="form_input_item">
              <view class="title">手机号</view>
              <EasyInput v-model="search.s_mobile" :max="11" type="number" placeholder="请输入" :isSearch="false" />
            </view>
            <view class="form_input_item">
              <view class="title">编号/昵称</view>
              <EasyInput v-model="search.s_name" placeholder="资源ID/线上ID/昵称/姓名" :isSearch="false" />
            </view>
            <view class="form_input_item">
              <view class="title">邀请单号</view>
              <EasyInput v-model="search.s_interno" placeholder="请输入" :isSearch="false" />
            </view>
            <view class="form_input_item">
              <view class="title">备注内容</view>
              <EasyTextarea v-model="search.s_remark" placeholder="请输入" />
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="footer">
        <view class="small-row">
          <view class="small-btn close-small-btn" @click="handleReset">重置</view>
          <view class="small-btn confirm-small-btn" @click="handleSubmit">确定</view>
        </view>
      </view>
    </view>
  </uni-drawer>
  <PickerView ref="pickerView" v-model="search.s_timetype" :options="pickerViewOptions" title="时间类型" />
</template>

<script setup>
import { ref, reactive, onMounted, defineEmits, getCurrentInstance, computed } from 'vue';
import EasyInput from '@/oeui/new/EasyInput';
import EasyTextarea from '@/oeui/new/EasyTextarea';
import dataRange from '../../../components/data-range.vue';
import PickerView from '@/oeui/new/PickerView';
import datePicker from '../../../oeui/new/picker/datePicker.vue';
import dayjs from 'dayjs';
const props = defineProps({
  pickerViewOptions:{ //时间类型
    type: Object,
    default: {},
  }
})
const emit = defineEmits(['search']);
const { proxy } = getCurrentInstance();
const drawer = ref(null);
const search = reactive({
  s_roleid: '', //部门
  s_makerid: '', //红娘ID
  s_addmkid: '', //录入红娘
  s_facemkid: '', //面谈红娘
  s_helpmkid: '', //协助红娘
  s_mobile: '', //手机号
  s_name: '', //昵称姓名
  s_ordsign: '', //签约状态
  s_flag: '', // 到店状态
  s_interno: '', //邀请单号
  s_followflag: '', //回访
  s_orderby: '', //排序
  s_remark: '', //备注
  s_mid: '', //资源ID
  s_timetype: 'intertime',
  s_time: '',
  s_date1: '',
  s_date2: ''
});
const timetypeName = computed(() => {
  return props.pickerViewOptions.filter(item => item.value == search.s_timetype)[0].text;
});
const initialSearchState = JSON.parse(JSON.stringify(search));
let savedRange = ref()
const getRange = item => {
  savedRange.value = {...item}
  Object.assign(search, item);
};
const savedDate = ref(); // 保存选中的日期
const getDate = val => {
  if (!val) {
    search.s_date1 = '';
    search.s_date2 = '';
  } else {
    search.s_date1 = dayjs(val[0]).format('YYYY-MM-DD');
    search.s_date2 = dayjs(val[1]).format('YYYY-MM-DD');
    savedDate.value = val; // 保存选中的日期
  }
};
const handleSubmit = () => {
  emit('search', search);
  back();
};
const showPickerView = () => {
  proxy.$refs.pickerView.open();
};
// 重置函数
const handleReset = () => {
  // 重置 search 对象
  Object.keys(search).forEach(key => {
    search[key] = initialSearchState[key];
  });
  savedDate.value = ''
  savedRange.value = ''
  emit('search', search);
  setTimeout(() => {
    back();
  }, 200);
};

const back = () => {
  drawer.value.close();
};

const open = () => {
  drawer.value.open();
};

onMounted(() => {});

defineExpose({
  open,
  close: () => drawer.value.close(),
  reset: handleReset
});
</script>

<style lang="scss" scoped>
.filter_content {
  .scroll-view-box {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .search_form_box {
    padding: 20rpx 32rpx;

    .form_input_rows {
      @include flex-column;

      .form_input_item {
        margin-bottom: 35rpx;

        &:last-child {
          margin-bottom: 48rpx;
        }

        .title {
          color: #000;
          font-size: 32rpx;
          margin-bottom: 24rpx;
        }
      }
      .form_picker_item {
        height: 84rpx;
        @include flex-align-center;
        @include flex-between;
        margin-bottom: 30rpx;

        .title {
          color: #000;
          font-size: 32rpx;
        }
        .picker_value {
          flex: 1;
          @include flex-align-center;
          justify-content: flex-end;
          font-size: 32rpx;
          min-width: 0;

          text.value {
            color: #373d49;
          }
          text {
            color: #c0c4cc;
            margin-right: 12rpx;
            font-size: 32rpx;
          }
          .value-container {
            flex: 1;
            min-width: 0;
            @include flex-align-center;
            justify-content: flex-end;
          }

          .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }

          .icon-Frame {
            font-size: 32rpx;
            margin: 0 10rpx 0 14rpx;
            color: #666;
          }

          .text,
          .no {
            margin-right: 8rpx;
          }

          .text {
            color: #666;

            .icon-Frame {
              color: #666;
            }
          }

          .no {
            color: #c0c4cc;
          }

          .iconfont {
            font-size: 32rpx;
            flex-shrink: 0;
          }
        }
      }
    }

    .form_slider_rows {
      @include flex-column;
      margin-bottom: 40rpx;

      .ages_row {
        color: #000;
        font-size: 32rpx;
        margin-bottom: 32rpx;

        .range {
          color: #6365e0;
        }
      }
    }

    .form_picker_rows {
      @include flex-column;

      .title {
        width: 160rpx;
        padding-right: 24rpx;
        font-size: 32rpx;
        color: #000;
        flex-shrink: 0;
      }

      .form_picker_item.group_btn_row {
        min-height: 84rpx;
      }

      .form_button_item {
        align-items: flex-start;
        flex-direction: column;
        margin: 16rpx 0 24rpx 0;
      }

      .form_picker_item {
        height: 84rpx;
        @include flex-align-center;
        @include flex-between;
        margin-bottom: 8rpx;

        .picker_value {
          flex: 1;
          @include flex-align-center;
          justify-content: flex-end;
          font-size: 32rpx;
          min-width: 0;

          .value-container {
            flex: 1;
            min-width: 0;
            @include flex-align-center;
            justify-content: flex-end;
          }

          .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }

          .icon-Frame {
            font-size: 32rpx;
            margin: 0 10rpx 0 14rpx;
            color: #666;
          }

          .text,
          .no {
            margin-right: 8rpx;
          }

          .text {
            color: #666;

            .icon-Frame {
              color: #666;
            }
          }

          .no {
            color: #c0c4cc;
          }

          .iconfont {
            font-size: 32rpx;
            color: #000;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .footer {
    height: 110rpx;
    @include flex-center;

    .small-row {
      @include flex-center;
      justify-content: space-between;
    }

    .small-btn {
      @include flex-center;
      width: 320rpx;
      height: 80rpx;
      background: #6365e0;
      color: #fff;
      font-size: 32rpx;
      border-radius: 40rpx;

      &:active {
        background: #8284e6;
      }
    }

    .close-small-btn {
      margin-right: 32rpx;
      background: #f7f7f9;
      color: #000;

      &:active {
        color: #fff;
      }
    }
  }
}
</style>
