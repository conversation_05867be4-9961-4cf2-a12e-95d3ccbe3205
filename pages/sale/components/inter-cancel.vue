<template>
  <PopupContainer
    titlePosition="center"
    title="取消邀约"
    :showClose="true"
    :maskClick="true"
    ref="followupPopup"
    @confirm="handleConfirm"
  >
    <template #content>
      <view class="followup">
        <view class="top flex flex_ac">
          <view class="top_l flex flex_ac">
            <SafeImage :src="info.member.headimg_url" class="img44 br8 mr8" />
          </view>
          <view class="top_r">
            <text class="fz16 color_font1">{{ info.member?.nickname }}</text>
            <view class="color_6 fz14">
              资源ID:{{ info.member?.mid }}
              <span v-if="info.member?.userid > 0"
                >&nbsp;/&nbsp;线上ID:{{ info.member?.userid }}</span
              >
              </view
            >
          </view>
        </view>
        <view class="mt12 pr">
          <view class="font-s fz16 mb12">备注信息</view>
          <EasyTextarea v-model="removeForm.cancelcont" :isBackground="false" placeholder="请输入" />
        </view>
      </view>
    </template>
  </PopupContainer>
</template>
<script setup>
import { ref, reactive, nextTick, getCurrentInstance } from "vue";
import { getInterlist } from "@/api/sale.js";
import SafeImage from "@/oeui/new/SafeImage.vue";
import EasyTextarea from "@/oeui/new/EasyTextarea.vue"
import PopupContainer from "../../../oeui/new/PopupContainer.vue";
import textareaPicker from "../../../oeui/new/picker/textareaPicker.vue";
const followupPopup = ref(null);
const info = ref();
const { proxy } = getCurrentInstance();
const removeForm = reactive({ id: "", cancelcont: "" });
// 打开向上弹框
const openPopup = async (data) => {
  info.value = data;
  removeForm.id = data.interid;
  await nextTick();
  if (followupPopup.value && typeof followupPopup.value.open === "function") {
    followupPopup.value.open();
  }
};
const handleConfirm = () => {
  if (!removeForm.cancelcont) {
    uni.showToast({
      title: "请输入取消原因",
      icon: "none",
    });
    return;
  }
  proxy.$showModal({
    title: "温馨提示",
    content: `确定取消该邀约吗？`,
    success(res) {
      uni.showLoading(); // 显示加载状态
      getInterlist({ a: "savecancel", ...removeForm })
        .then(() => {
          uni.showToast({
            title: "已取消",
            icon: "success",
          });
          followupPopup.value.close();
        })
        .finally(() => uni.hideLoading());
    },
  });
};
defineExpose({ openPopup });
</script>
<style lang="scss" scoped>
</style>
