<template>
  <PopupContainer
    titlePosition="center"
    title="邀约回访"
    :showClose="true"
    :maskClick="true"
    ref="followupPopup"
    @confirm="handleConfirm"
  >
    <template #content>
      <view class="followup">
        <view class="mt12">
          <timePicker
            title="到期时间"
            @timeChanged="timeChanged"
            :initialTime="addForm.intertime_t"
          />
        </view>
        <view class="flex_dc mt12 pr">
          <view class="font-s fz16">面谈红娘</view>
          <view class="picker_value">
            <view @click="proxy.$refs.tree.open()" class="value-container">
              <view v-if="addForm.facemkid" class="text text-ellipsis">{{
                facemkName
              }}</view>
              <view v-else class="no">不限</view>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>
        </view>
        <view class="mt12 pr">
          <view class="font-s fz16 mb12">预测套餐</view>
          <view class="flex flex_warp">
            <view
              v-for="item in packagePicker"
              class="picker_btn"
              :class="{ active_btn: item.tcid == addForm.tcid }"
              :key="item.tcid"
              @click="addForm.tcid = item.tcid"
              >{{ item.tcname }}</view
            >
          </view>
        </view>
        <view class="mt12 pr">
          <view class="font-s fz16 mb12">备注信息</view>
          <EasyTextarea v-model="addForm.remark" :isBackground="false" placeholder="请输入" />
        </view>
      </view>
    </template>
  </PopupContainer>
  <Tree
    ref="tree"
    v-model="addForm.facemkid"
    :tree="picker.makerdata"
    title="录入红娘"
    @confirm="handleConfirmTree"
  />
</template>
<script setup>
import { ref, reactive, nextTick, getCurrentInstance } from "vue";
import { postInterfollowup } from "@/api/sale.js";
import { getPackagepicker } from "@/api/common.js";
import EasyTextarea from '@/oeui/new/EasyTextarea';
import PopupContainer from "@/oeui/new/PopupContainer.vue";
import timePicker from "@/oeui/new/picker/timePicker.vue";
import { getInterlist } from "@/api/sale";
import Tree from "@/oeui/new/Tree/Tree";
import dayjs from "dayjs";
const { proxy } = getCurrentInstance();
const followupPopup = ref(null);
const addForm = reactive({
  mid: "",
  facemkid: "",
  remark: "",
  intertime_t: "", //邀约时间
  tcid: "",
});
const facemkName = ref();
const picker = reactive({
  makerdata: [],
});
const initPicker = () => {
  for (const key in picker) {
    try {
      const storedValue = uni.getStorageSync(key);
      if (storedValue) {
        picker[key] = JSON.parse(storedValue);
      } else {
        picker[key] = [];
      }
    } catch (error) {
      picker[key] = [];
    }
  }
};
const packagePicker = ref();
function getPicker() {
  getPackagepicker().then((res) => {
    if (res.ret == 1) {
      packagePicker.value = res.result.data;
    }
  });
}

function getInfo(id) {
  getInterlist({ a: "edit", id }).then((res) => {
    if (res.ret == 1) {
      let data = res.result.data
      for (let key in addForm) {
        if (key == "intertime_t") {
          addForm[key] = new Date(parseInt(data.intertime) * 1000);
        } else {
          addForm[key] = data[key];
        }
      }
      facemkName.value = data.face_maker.name;
      addForm.id = res.result.id;
    }
  });
}
// 打开向上弹框
const openPopup = async (data) => {
  if (data.interid) {
    await getInfo(data.interid);
  }

  getPicker();
  initPicker();
  await nextTick();
  if (followupPopup.value && typeof followupPopup.value.open === "function") {
    followupPopup.value.open();
  }
};
const timeChanged = (e) => {
  addForm.intertime_t = e;
};
const handleConfirmTree = (e) => {
  if (!e?.makerid) return;
  facemkName.value = e.name;
};
const handleConfirm = () => {
  if (addForm.cancelflag == 0 && !addForm.intertime_t) {
    uni.showToast({
      title: "请选择到店日期",
      icon: "none",
    });
    return;
  }
  addForm.intertime_t = dayjs(addForm.intertime_t).format('YYYY-MM-DD HH:mm:ss')
  uni.showLoading(); // 显示加载状态
  getInterlist({ a: "saveedit", ...addForm })
    .then(() => {
      uni.showToast({
        title: "编辑成功",
        icon: "success",
      });
      followupPopup.value.close();
    })
    .finally(() => uni.hideLoading());
};
defineExpose({ openPopup });
</script>
<style lang="scss" scoped>
.followup {
  .textarea {
    width: 100%;
    height: 200rpx;
    border: 1px solid #ccc;
    border-radius: 10rpx;
    padding: 20rpx;
    box-sizing: border-box;
    resize: none;
  }
  .font-s {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 主标题色 */
    color: #000000;
  }
  .picker_btn {
    margin: 0px 20rpx 20rpx 0;
    border-radius: 8px;
    opacity: 1;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    /* 副标色 */
    color: #666666;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    padding: 12rpx 28rpx;
    /* 背景灰 */
    background: #f7f7f9;
  }
  .active_btn {
    background: #6365e0;
    color: #fff;
  }
  .picker_value {
    flex: 1;
    @include flex-align-center;
    justify-content: flex-end;
    font-size: 32rpx;
    min-width: 0;

    .value-container {
      flex: 1;
      min-width: 0;
      @include flex-align-center;
      justify-content: flex-end;
    }

    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .icon-Frame {
      font-size: 32rpx;
      margin: 0 10rpx 0 14rpx;
      color: #666;
    }

    .text,
    .no {
      margin-right: 8rpx;
    }

    .text {
      color: #666;

      .icon-Frame {
        color: #666;
      }
    }

    .no {
      color: #c0c4cc;
    }

    .iconfont {
      font-size: 32rpx;
      color: #000;
      flex-shrink: 0;
    }
  }
}
</style>
