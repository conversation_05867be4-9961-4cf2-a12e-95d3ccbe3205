import { createStore } from 'vuex'
import http from '@/utils/api.js';
import createPersistedState from 'vuex-persistedstate'

export default createStore({
	state: {
		siteUrl: 'https://e.oephp.com/',
		config: {},
		loginInfo: {},
		listState: false,
		loginStatus: 0, //0:未登录  1:已登录
		uploadType: 1,
		firstyear: 2010,
	},
	mutations: {
		//更新登录状态
		updataLoginStatus(state, id) {
			if (id) {
				state.loginStatus = 1
			} else {
				state.loginStatus = 0
			}
		},

		//设置用户基本资料
		setloginInfo(state, info) {
			state.loginInfo = Object.assign({}, state.loginInfo, info)
		},

		setFirstyear(state, val) {
			state.firstyear = val || 2010;
		},

		//设置站点配置
		setConfig(state, info) {
			state.config = info
			state.siteUrl = info.siteurl
		},

		setUploadType(state, value) {
			state.uploadType = value
		},

		setIncome(state, data) {
			state.income = data
		}
	},
	actions: {
		//获取站点配置
		getConfig({ commit }, isGetHc = false) {
			if (isGetHc) {
				let hcConfig = uni.getStorageSync('hcConfig') || null
				if (hcConfig) {
					commit('setConfig', JSON.parse(hcConfig).result)
					return new Promise((resolve, reject) => {
						resolve(JSON.parse(hcConfig))
					})
				}
			}
			const p = http.post('', {
				m: 'vuewap',
				c: 'picker',
				a: 'config'
			})
			p.then(res => {
				if (res.ret == 1) {
					commit('setConfig', res.result)
					uni.setStorageSync('hcConfig', JSON.stringify(res))
					uni.setStorageSync('smsCheck', res.result.regmobilecode)
				}
			})
			return p
		},

		//设置用户登录信息
		setUserInfo({ commit }, result) {
			if (result.sign) uni.setStorageSync('maker_sign', result.sign)
			commit('setloginInfo', result.info)
		},

		//获取用户登录信息
		getUserInfo({ commit }) {
			let sign = uni.getStorageSync('maker_sign')
			if (sign) {
				const p = http.post('', {
					m: 'crm',
					c: 'my',
				})
				p.then(res => {
					if (res.ret == 1) {
						commit('setloginInfo', res.result.info)
						commit('setFirstyear', res.result.the_firstyear)
					}
				})
				return p
			} else {
				return new Promise((resolve, reject) => {
					commit('updataLoginStatus', 0)
					resolve({ ret: -10 })
				})
			}
		},
	},
	plugins: [
		createPersistedState({
			key: 'vuex',
			storage: {
				getItem: key => uni.getStorageSync(key),
				setItem: (key, value) => uni.setStorageSync(key, value),
				removeItem: key => uni.removeStorageSync(key)
			}
		})
	]
})