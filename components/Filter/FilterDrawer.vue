<template>
  <uni-drawer ref="drawer" mode="right" :mask="false" :mask-click="false">
    <view class="filter_content">
      <CustomNavBar :isBack="false" title="筛选" showLeft @back="back" />
      <scroll-view style="height: calc(100vh - 92rpx - 110rpx)" class="scroll-view-box" scroll-y>
        <view class="search_form_box">
          <view class="form_input_rows">
            <view class="form_input_item">
              <view class="title">手机号</view>
              <EasyInput v-model="search.s_mobile" :max="11" type="number" placeholder="请输入" :isSearch="false" />
            </view>
            <view class="form_input_item">
              <view class="title">编号/昵称</view>
              <EasyInput v-model="search.s_name" placeholder="资源ID/线上ID/昵称/姓名" :isSearch="false" />
            </view>
          </view>

          <view class="form_slider_rows">
            <view class="ages_row flex flex_ac">
              <text>年龄范围：</text>
              <template v-if="search.s_age1 && search.s_age2">
                <template v-if="isMinAge">
                  <text class="range">{{ search.s_age1 }}岁以上</text>
                </template>
                <template v-else-if="isMaxAge">
                  <text class="range">{{ search.s_age2 }}岁以下</text>
                </template>
                <template v-else-if="!isAgeRangeDefault">
                  <text class="range">{{ search.s_age1 }}-{{ search.s_age2 }}岁</text>
                </template>
              </template>
              <template v-else>不限</template>
            </view>
            <RangeSlider
              ref="ageSlider"
              @rangechange="handleAgeRange"
              :values="[rangeSlider.initMinAge, rangeSlider.initMaxAge]"
              :min="rangeSlider.minAge"
              :max="rangeSlider.maxAge"
              :width="686"
              :height="40"
            />
          </view>

          <view class="form_picker_rows">
            <view class="form_picker_item">
              <view class="title">默认排序</view>
              <view class="picker_value">
                <view @click="proxy.$refs.sort.open()" class="value-container">
                  <view v-if="search.s_orderby" class="text text-ellipsis flex flex_ac">
                    {{ getSortName(search.s_orderby) }}
                    <template v-if="search.s_orderby.includes('desc')">
                      <text class="iconfont icon-Frame"></text>
                      <text>降序</text>
                    </template>
                    <template v-else class="flex flex_ac">
                      <text style="transform: scaleY(-1)" class="iconfont icon-Frame"></text>
                      <text>升序</text>
                    </template>
                  </view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_picker_item group_btn_row">
              <view class="title">是否白板</view>
              <GroupButton v-model="search.s_whiteboard" :options="whiteboardOptions" />
            </view>

            <view class="form_picker_item">
              <view class="title">导入批次</view>
              <view class="picker_value">
                <view @click="showPickerView('导入批次', 'voimport', 's_snid')" class="value-container">
                  <view v-if="search.s_snid" class="text text-ellipsis">{{ getOptionText('voimport', search.s_snid) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>
            <view class="form_picker_item">
              <view class="title">线下活动</view>
              <view class="picker_value">
                <view @click="showPickerView('线下活动', 'voparty', 's_partyid')" class="value-container">
                  <view v-if="search.s_partyid" class="text text-ellipsis">{{ getOptionText('voparty', search.s_partyid) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>
            <view class="form_picker_item">
              <view class="title">互选活动</view>
              <view class="picker_value">
                <view @click="showPickerView('互选活动', 'voactivity', 's_actid')" class="value-container">
                  <view v-if="search.s_actid" class="text text-ellipsis">{{ getOptionText('voactivity', search.s_actid) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>
            <view class="form_picker_item">
              <view class="title">SEM投放</view>
              <view class="picker_value">
                <view @click="showPickerView('SEM投放', 'vosem', 's_wayid')" class="value-container">
                  <view v-if="search.s_wayid" class="text text-ellipsis">{{ getOptionText('vosem', search.s_wayid) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>
            <view class="form_picker_item">
              <view class="title" style="width: 220rpx">
                <text>资源来源(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <view class="picker_value">
                <view @click="showPicker('资源来源', 'crm_fromcat', 'fromcat')" class="value-container">
                  <view v-if="searchMore.fromcat" class="text text-ellipsis">{{ formatSelectedText(searchMore.fromcat, picker.crm_fromcat) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>
            <view class="form_picker_item">
              <view class="title">录入红娘</view>
              <view class="picker_value">
                <view @click="proxy.$refs.tree.open()" class="value-container">
                  <view v-if="search.s_addmkid" class="text text-ellipsis">{{ search.s_addmkName }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_picker_item group_btn_row">
              <view class="title">有无帮约</view>
              <GroupButton v-model="search.s_after" :options="afterOptions" />
            </view>

            <view class="form_picker_item">
              <view class="title">标签</view>
              <view class="picker_value">
                <view @click="showPicker('标签', 'crm_tags', 'tags')" class="value-container">
                  <view v-if="searchMore.tags" class="text text-ellipsis">{{ formatSelectedText(searchMore.tags, picker.crm_tags) }}</view>
                  <view v-else class="no">不限</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_picker_item group_btn_row">
              <view class="title">性别</view>
              <GroupButton v-model="search.s_gender" :options="genderOptions" />
            </view>

            <view class="form_button_item">
              <view class="title" style="width: 220rpx; margin-bottom: 24rpx">
                <text>婚况(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <ButtonSelector v-model="searchMore.marry" multiple :options="picker.marry" />
            </view>

            <view class="form_picker_item group_btn_row">
              <view class="title">有无头像</view>
              <GroupButton v-model="search.s_avatar" :options="afterOptions" />
            </view>

            <view class="form_picker_item">
              <view class="title">居住地</view>
              <view class="picker_value">
                <view @click="showArea('选择居住地', 'district')" class="value-container">
                  <view v-if="areaSearch.district.text" class="text text-ellipsis">{{ areaSearch.district.text }}</view>
                  <view v-else class="no">请选择</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_picker_item">
              <view class="title">户籍地</view>
              <view class="picker_value">
                <view @click="showArea('选择户籍地', 'hometown')" class="value-container">
                  <view v-if="areaSearch.hometown.text" class="text text-ellipsis">{{ areaSearch.hometown.text }}</view>
                  <view v-else class="no">请选择</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_button_item">
              <view class="title" style="width: 220rpx; margin-bottom: 24rpx">
                <text>学历(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <ButtonSelector v-model="searchMore.education" multiple :options="picker.education" />
            </view>

            <view class="form_picker_item">
              <view class="title" style="width: 200rpx">
                <text>职业(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <view class="picker_value">
                <view @click="showPicker('选择职业', 'job', 'job')" class="value-container">
                  <view v-if="searchMore.job" class="text text-ellipsis">{{ formatSelectedText(searchMore.job, picker.job) }}</view>
                  <view v-else class="no">请选择</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_button_item">
              <view class="title" style="width: 220rpx; margin-bottom: 24rpx">
                <text>年收入(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <ButtonSelector v-model="searchMore.salary" multiple :options="picker.salary" />
            </view>

            <view class="form_picker_item">
              <view class="title" style="width: 200rpx">
                <text>民族(</text>
                <text style="color: #8898b6">多选</text>
                <text>)</text>
              </view>
              <view class="picker_value">
                <view @click="showPicker('选择民族', 'national', 'national')" class="value-container">
                  <view v-if="searchMore.national" class="text text-ellipsis">{{ formatSelectedText(searchMore.national, picker.national) }}</view>
                  <view v-else class="no">请选择</view>
                </view>
                <view class="iconfont icon-youjiantou1"></view>
              </view>
            </view>

            <view class="form_button_item">
              <view class="title" style="width: 220rpx; margin-bottom: 24rpx">
                <text>备注内容</text>
              </view>
              <EasyInput v-model="search.s_remark" placeholder="请输入" :isSearch="false" />
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="footer">
        <view class="small-row">
          <view class="small-btn close-small-btn" @click="handleReset">重置</view>
          <view class="small-btn confirm-small-btn" @click="handleSubmit">确定</view>
        </view>
      </view>
    </view>
  </uni-drawer>

  <PickerView ref="pickerView" v-model="search[pickerViewKey]" :options="pickerViewOptions" :title="pickerViewTitle" @confirm="handleConfirmPickerView" />
  <Picker ref="multiplePicker" v-model="searchMore[pickerKey]" :options="pickerOptions" multiple :title="pickerTitle" @confirm="handleConfirmPicker" />

  <Sort ref="sort" v-model="search.s_orderby" :options="sortlist" title="选择排序" />
  <Area v-if="areaKey" ref="area" v-model="areaSearch[areaKey].area" :title="areaTitle" @confirm="handleConfirmArea" />
  <Tree ref="tree" v-model="search.s_addmkid" :tree="picker.makerdata" title="录入红娘" @confirm="handleConfirmTree" />
</template>

<script setup>
import { ref, reactive, onMounted, defineEmits, computed, getCurrentInstance, nextTick } from 'vue';
import { useStore } from 'vuex';

import EasyInput from '@/oeui/new/EasyInput';
import RangeSlider from '@/oeui/new/RangeSlider';
import Picker from '@/oeui/new/Picker';
import PickerView from '@/oeui/new/PickerView';
import Tree from '@/oeui/new/Tree/Tree';
import Area from '@/oeui/new/Area';
import GroupButton from '@/oeui/new/GroupButton';
import ButtonSelector from '@/oeui/new/ButtonSelector';
import Sort from '@/components/Sort/Sort';

import { getMinMaxValues } from '@/utils/hooks.js';
import { postCrmPicker } from '@/api/common.js';
import { sortlist, whiteboardOptions, afterOptions, genderOptions } from '@/assets/staticData';

const { proxy } = getCurrentInstance();

const emit = defineEmits(['search']);

const drawer = ref(null);
const store = useStore();
const pwscope = store.state?.loginInfo?.pwscope;

const rangeSlider = reactive({
  minAge: 0,
  maxAge: 0,
  initMinAge: 0,
  initMaxAge: 0
});

let pickerViewOptions = [];
let pickerViewTitle = ref('');
let pickerViewKey = ref('');

let pickerOptions = [];
let pickerTitle = ref('');
let pickerKey = ref('');

let areaTitle = ref('');
let areaKey = ref('');

const areaSearch = reactive({
  district: { text: '', area: {}, ids: [] },
  hometown: { text: '', area: {}, ids: [] }
});

const picker = reactive({
  crm_fromcat: [], //数据来源
  sem: [], //SEM投放
  district: [],
  hometown: [],
  gender: [],
  salary: [],
  education: [],
  age: [],
  signflag: [], //签约状态
  crm_grade: [], //客户分类
  national: [], //民族
  marry: [], //婚况
  job: [],
  crm_tags: [],
  party: [],
  activity: [],
  crmimport: [],
  makerdata: []
});

const netpicker = reactive({
  vosem: [],
  voparty: [],
  voactivity: [],
  voimport: []
});

const search = reactive({
  s_addmkid: '', //录入红娘
  s_mid: '',
  s_mobile: '', //手机号
  s_name: '', //昵称姓名
  s_snid: '', //导入批次
  s_wayid: '', //SEM投放
  s_partyid: '', //线下活动
  s_actid: '', //互选
  s_after: '', //红娘帮约
  s_unionid: '', //推广红娘
  s_remark: '', //备注
  s_dist: '',
  s_home: '',
  s_gender: '',
  s_age1: '',
  s_age2: '',
  s_binduid: '', //线上用户
  s_orderby: '', //排序
  s_avatar: '', //头像
  s_timetype: '',
  s_time: '',
  s_date1: '',
  s_date2: '',
  s_whiteboard: '',
  s_timetype: 'addtime'
});

const searchMore = reactive({
  fromcat: '',
  tags: '',
  marry: '',
  national: '',
  job: '',
  salary: '',
  education: ''
});

const initialSearchState = JSON.parse(JSON.stringify(search));
const initialSearchMoreState = JSON.parse(JSON.stringify(searchMore));
const initialAreaSearchState = JSON.parse(JSON.stringify(areaSearch));

const isAgeRangeDefault = computed(() => {
  return Number(search.s_age1) === rangeSlider.minAge && Number(search.s_age2) === rangeSlider.maxAge;
});

const isMinAge = computed(() => {
  return Number(search.s_age2) === rangeSlider.maxAge && Number(search.s_age1) > rangeSlider.minAge;
});

const isMaxAge = computed(() => {
  return Number(search.s_age1) === rangeSlider.minAge && Number(search.s_age2) < rangeSlider.maxAge;
});

const getNetPicker = async () => {
  try {
    const FIELD_MAPPING = {
      voimport: { text: 'logsn', value: 'snid' }, // 导入批次
      voactivity: { text: 'title', value: 'actid' }, // 活动
      voparty: { text: 'title', value: 'partyid' }, // 聚会
      vosem: { text: 'title', value: 'wayid' } // 渠道
    };

    const requests = Object.keys(netpicker).map(key =>
      postCrmPicker({ a: key }).then(res => {
        if (res.ret !== 1) return { key, data: [] };

        const mapping = FIELD_MAPPING[key];
        if (!mapping) return { key, data: [] };

        // 处理数据结构
        const transformedData = res.result.data.map(item => ({
          text: item[mapping.text],
          value: item[mapping.value]
        }));

        return { key, data: transformedData };
      })
    );

    const results = await Promise.all(requests);
    results.forEach(({ key, data }) => {
      netpicker[key] = data;
    });
  } catch (error) {
    console.error(error);
  }
};

// 监听年龄范围
const handleAgeRange = e => {
  search.s_age1 = e.minValue;
  search.s_age2 = e.maxValue;

  // 处理不限年龄的情况
  if (isAgeRangeDefault.value) {
    search.s_age1 = '';
    search.s_age2 = '';
  }
};

const showArea = async (title, key) => {
  areaTitle.value = title;
  areaKey.value = key;
  await nextTick();
  setTimeout(() => {
    proxy.$refs.area.open(key);
  }, 0);
};

// 处理显示PickerView弹窗
const showPickerView = (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);

  pickerViewKey.value = key;
  pickerViewTitle.value = title;
  pickerViewOptions = OPTIONS;
  proxy.$refs.pickerView.open();
};

// 处理显示Picker单选、多选弹窗
const showPicker = (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);

  pickerKey.value = key;
  pickerTitle.value = title;
  pickerOptions = OPTIONS;
  proxy.$refs.multiplePicker.open();
};

const handleConfirmPickerView = e => {
  search[pickerViewKey.value] = e.value;
};

const handleConfirmPicker = e => {
  searchMore[pickerKey.value] = e.map(item => item.value).join(',');
};

// 选择地区
const handleConfirmArea = e => {
  areaSearch[areaKey.value].area = e?.area;
  areaSearch[areaKey.value].ids = e?.ids;
  areaSearch[areaKey.value].text = e?.text;
};

const handleConfirmTree = e => {
  if (!e?.makerid) return;
  search.s_addmkName = e.name;
};

const getOptionText = (optionsKey, value) => {
  const OPTIONS_MAP = getOptionsByKey(optionsKey);
  if (!OPTIONS_MAP || !OPTIONS_MAP.length) return '';

  const foundItem = OPTIONS_MAP.find(item => item.value === value);
  return foundItem ? foundItem.text : '';
};

const formatSelectedText = (selectedValueStr, options) => {
  if (!selectedValueStr) return '';
  const selectedValues = selectedValueStr.split(',');
  const selectedTexts = selectedValues
    .map(value => {
      const found = options.find(option => option.value === Number(value));
      return found ? found.text : '';
    })
    .filter(text => text);
  return selectedTexts.join('、') || '';
};

const getSortName = sortValue => {
  if (!sortValue) return '';
  const [field, order] = sortValue.split('_');
  const option = sortlist.find(item => item.value === field);
  if (!option) return sortValue;
  return option.text;
};

const getOptionsByKey = key => {
  const OPTIONS_MAP = {
    sortlist,
    voimport: netpicker.voimport,
    vosem: netpicker.vosem,
    voparty: netpicker.voparty,
    voactivity: netpicker.voactivity,
    crm_fromcat: picker.crm_fromcat,
    crm_tags: picker.crm_tags,
    crm_marry: picker.crm_marry,
    job: picker.job,
    national: picker.national
  };

  return OPTIONS_MAP[key] || [];
};

const handleSubmit = () => {
  const params = {
    ...search,
    ...searchMore,
    s_dist: JSON.stringify(areaSearch.district.ids),
    s_home: JSON.stringify(areaSearch.hometown.ids),
    s_age1: search.s_age1 || '',
    s_age2: search.s_age2 || ''
  };

  emit('search', params);
  back();
};

// 重置函数
const handleReset = () => {
  // 重置 search 对象
  Object.keys(search).forEach(key => {
    search[key] = initialSearchState[key];
  });

  // 重置 searchMore 对象
  Object.keys(searchMore).forEach(key => {
    searchMore[key] = initialSearchMoreState[key];
  });

  // 重置地区选择
  Object.keys(areaSearch).forEach(key => {
    areaSearch[key] = JSON.parse(JSON.stringify(initialAreaSearchState[key]));
  });

  // 重置年龄范围滑块
  search.s_age1 = '';
  search.s_age2 = '';
  proxy.$refs.ageSlider?.reset();

  // 重置红娘选择
  search.s_addmkid = '';
  search.s_addmkName = '';
  proxy.$refs.tree?.close();

  const params = {
    ...search,
    ...searchMore,
    s_dist: areaSearch.district.ids,
    s_home: areaSearch.hometown.ids,
    s_age1: search.s_age1 || '',
    s_age2: search.s_age2 || ''
  };

  emit('search', params);
  setTimeout(() => {
    back();
  }, 200);
};

const back = () => {
  drawer.value.close();
};

const initAges = () => {
  const agesStr = uni.getStorageSync('age');
  if (agesStr) {
    const ages = JSON.parse(agesStr);
    const { min, max } = getMinMaxValues(ages);
    rangeSlider.minAge = min;
    rangeSlider.maxAge = max;
  }
};

const initPicker = () => {
  for (const key in picker) {
    try {
      const storedValue = uni.getStorageSync(key);
      if (storedValue) {
        picker[key] = JSON.parse(storedValue);
      } else {
        picker[key] = [];
      }
    } catch (error) {
      picker[key] = [];
    }
  }
};

const open = () => {
  getNetPicker();
  drawer.value.open();
  rangeSlider.initMinAge = search.s_age1 ? Number(search.s_age1) : rangeSlider.minAge;
  rangeSlider.initMaxAge = search.s_age2 ? Number(search.s_age2) : rangeSlider.maxAge;
};

onMounted(() => {
  initPicker();
  initAges();
});

defineExpose({
  open,
  close: () => drawer.value.close(),
  reset: handleReset
});
</script>

<style lang="scss" scoped>
.filter_content {
  .scroll-view-box {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .search_form_box {
    padding: 20rpx 32rpx;

    .form_input_rows {
      @include flex-column;

      .form_input_item {
        margin-bottom: 32rpx;

        &:last-child {
          margin-bottom: 48rpx;
        }

        .title {
          color: #000;
          font-size: 32rpx;
          margin-bottom: 24rpx;
        }
      }
    }

    .form_slider_rows {
      @include flex-column;
      margin-bottom: 40rpx;

      .ages_row {
        color: #000;
        font-size: 32rpx;
        margin-bottom: 32rpx;

        .range {
          color: #6365e0;
        }
      }
    }

    .form_picker_rows {
      @include flex-column;

      .title {
        width: 160rpx;
        padding-right: 24rpx;
        font-size: 32rpx;
        color: #000;
        flex-shrink: 0;
      }

      .form_picker_item.group_btn_row {
        min-height: 84rpx;
      }

      .form_button_item {
        align-items: flex-start;
        flex-direction: column;
        margin: 16rpx 0 24rpx 0;
      }

      .form_picker_item {
        height: 84rpx;
        @include flex-align-center;
        @include flex-between;
        margin-bottom: 8rpx;

        .picker_value {
          flex: 1;
          @include flex-align-center;
          justify-content: flex-end;
          font-size: 32rpx;
          min-width: 0;

          .value-container {
            flex: 1;
            min-width: 0;
            @include flex-align-center;
            justify-content: flex-end;
          }

          .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }

          .icon-Frame {
            font-size: 32rpx;
            margin: 0 10rpx 0 14rpx;
            color: #666;
          }

          .text,
          .no {
            margin-right: 8rpx;
          }

          .text {
            color: #666;

            .icon-Frame {
              color: #666;
            }
          }

          .no {
            color: #c0c4cc;
          }

          .iconfont {
            font-size: 32rpx;
            color: #000;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .footer {
    height: 110rpx;
    @include flex-center;

    .small-row {
      @include flex-center;
      justify-content: space-between;
    }

    .small-btn {
      @include flex-center;
      width: 320rpx;
      height: 80rpx;
      background: #6365e0;
      color: #fff;
      font-size: 32rpx;
      border-radius: 40rpx;

      &:active {
        background: #8284e6;
      }
    }

    .close-small-btn {
      margin-right: 32rpx;
      background: #f7f7f9;
      color: #000;

      &:active {
        color: #fff;
      }
    }
  }
}
</style>
