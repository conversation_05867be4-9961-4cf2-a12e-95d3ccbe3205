<template #content>
  <view class="range_popup">
    <view class="range_type">
      <view
        class="range_type_item"
        :class="getRangeItemClass(item)"
        v-for="item in viewTypes"
        :key="item.value"
        @click="handleViewType(item)"
      >
        {{ item.text }}
      </view>
    </view>
    <TreeView
      v-model="rangeForm.s_roleid"
      :tree="netpicker.depart"
      :disabled="treeDisabled"
      title="选择部门"
      v-if="isClass"
      @confirm="handleDepartment"
    />
    <Tree
      v-model="rangeForm.s_makerid"
      :tree="netpicker.maker"
      :disabled="treeDisabled"
      :isShowRow="isSale"
      @confirm="handleTree"
    />
  </view>
</template>
<script setup>
import { reactive, computed, onMounted, watch} from "vue";
import { useStore } from "vuex";
import { usePermission } from "@/composables/usePermission";
import Tree from "@/oeui/new/Tree/Tree";
import { getClassAll, getCalssMaker } from "@/api/common.js";
import TreeView from "@/oeui/new/TreeView/TreeView";
const store = useStore();
const { hasPermission } = usePermission();
const props = defineProps({
  isClass: {
    // 选择部门
    type: Boolean,
    default: true,
  },
  isSale: {
    // 选择红娘
    type: Boolean,
    default: true,
  },
  defaultRange: { // 新增默认数据范围属性
    type: Object,
    default: () => ({})
  }
});
const emits = defineEmits(["confirm"]);
const hasWorkPower = hasPermission("work", "work_view");
const hasMyWorkPower = hasPermission("mywork", "mywork_view");
// 数据管理
const netpicker = reactive({
  depart: [],
  maker: [],
});
// 搜索参数
const search = reactive({
  s_date1: "",
  s_date2: "",
  s_time: "",
  s_roleid: "",
  s_makerid: "",
  viewtype: store.state?.loginInfo?.pwscope,
  s_refresh: "",
});
const rangeForm = reactive({
  s_roleid: "",
  s_makerid: "",
  viewtype: search.viewtype,
});
const allViewTypes = Object.freeze([
  { text: "本人数据", value: "1" },
  { text: "部门数据", value: "2" },
  { text: "门店数据", value: "3" },
]);

const treeDisabled = computed(() => rangeForm.viewtype === "1");
const viewTypes = computed(() => {
  const { pwscope } = store.state.loginInfo;
  return allViewTypes.filter((item) => {
    if (pwscope === "3") return true;
    if (pwscope === "2") return Number(item.value) <= 2;
    if (pwscope === "1") return item.value === "1";
    return false;
  });
});
const getRangeItemClass = computed(() => {
  return (item) => ({
    active: rangeForm.viewtype === item.value,
    disabled:
      (item.value === "1" && !hasMyWorkPower) ||
      (["2", "3"].includes(item.value) && !hasWorkPower),
  });
});

// 监听部门选择
const handleDepartment = (node) => {
  Object.assign(rangeForm, { s_roleid: node.roleid, s_makerid: "" });
  emits("confirm", rangeForm);
  getMakerData(rangeForm.viewtype);
};

// 监听数据范围类型
const handleViewType = (item) => {
  if (item.value === "1" && !hasMyWorkPower) return;
  if (["2", "3"].includes(item.value) && !hasWorkPower) return;

  Object.assign(rangeForm, {
    viewtype: item.value,
    s_roleid: "",
    s_makerid: "",
  });

  emits("confirm", rangeForm);
};
onMounted(() => {
});
const handleTree = (item) => {
  Object.assign(rangeForm, { s_makerid: item.makerid });
  emits("confirm", rangeForm);
};
// 获取部门数据
const getClassData = async (type) => {
  const viewtype = type || rangeForm.viewtype;
  const a = viewtype === "2" ? "mydepart" : "sitedepart";
  const res = await getClassAll({ viewtype, a });
  if (res.ret === 1) {
    netpicker.depart = res.result.data.map((item) => ({ first: 1, ...item }));
  }
};

// 获取运营人员数据
const getMakerData = async (type) => {
  const viewtype = type || rangeForm.viewtype;
  const res = await getCalssMaker({ viewtype, s_roleid: rangeForm.s_roleid });
  if (res.ret === 1) {
    netpicker.maker = res.result.data;
  }
};
// 监听 defaultRange 变化
watch(
  () => props.defaultRange,
  async (newRange) => {
    if (search.viewtype !== 1) {
      if (props.isClass) await getClassData(search.viewtype);
      if (props.isSale) await getMakerData(search.viewtype);
    }
    Object.assign(rangeForm, newRange);
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
.range_popup {
  padding: 16rpx 0 0;

  .range_type {
    @include flex-wrap;
    margin-bottom: 20rpx;

    &_item {
      width: 168rpx;
      height: 64rpx;
      @include flex-center;
      font-size: 28rpx;
      color: #000;
      cursor: pointer;
      background: #f7f7f9;
      box-sizing: border-box;
      border-radius: 16rpx;
      margin-right: 24rpx;

      &.active {
        background: #6365e0;
        color: #fff;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .range_option {
    @include flex-between;
    height: 84rpx;
    margin-bottom: 8rpx;

    &.disabled {
      opacity: 0.5;
    }

    &_title {
      width: 160rpx;
      font-size: 32rpx;
      color: #000;
    }

    &_content {
      @include flex-align-center;
      justify-content: flex-end;
      flex: 1;
      height: 100%;

      text {
        color: #c0c4cc;
        margin-right: 12rpx;
        font-size: 32rpx;
      }
    }
  }
}
</style>
