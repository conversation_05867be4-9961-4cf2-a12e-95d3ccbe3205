<template>
  <PopupContainer ref="popup" :title="title" :isDefaultPad="false" :hideFooter="true" @close="close">
    <template #content>
      <view class="sort_list">
        <view v-for="item in options" :key="item.value" class="sort_list_item">
          <view class="title">{{ item.text }}</view>
          <view class="operate">
            <view class="desc" :class="{ active: isActive(item.value, 'desc') }" @click="handleSort(item.value, 'desc')">
              <text class="iconfont icon-Frame"></text>
              <text>降序</text>
            </view>
            <view class="asc" :class="{ active: isActive(item.value, 'asc') }" @click="handleSort(item.value, 'asc')">
              <text class="iconfont icon-Frame"></text>
              <text>升序</text>
            </view>
          </view>
        </view>
        <view class="sort_list_default" :class="{ active: !currentSort.field }" @click="handleDefault">默认排序</view>
      </view>
    </template>
  </PopupContainer>
</template>

<script setup>
  import { ref, computed, defineProps, defineEmits, defineExpose, watch, onMounted } from 'vue';
  import PopupContainer from '@/oeui/new/PopupContainer';

  const props = defineProps({
    title: {
      type: String,
      default: '标题'
    },
    options: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['update:modelValue']);

  const popup = ref(null);

  const currentSort = ref({
    field: '',
    order: ''
  });

  const initCurrentSort = () => {
    if (!props.modelValue) {
      currentSort.value = { field: '', order: '' };
      return;
    }

    const [field, order] = props.modelValue.split('_');
    currentSort.value = { field, order };
  };

  onMounted(() => {
    initCurrentSort();
  });

  watch(
    () => props.modelValue,
    () => {
      initCurrentSort();
    },
    { immediate: true }
  );

  const isActive = (field, order) => {
    return currentSort.value.field === field && currentSort.value.order === order;
  };

  const handleSort = (field, order) => {
    currentSort.value = { field, order };
    const sortValue = `${field}_${order}`;
    emit('update:modelValue', sortValue);
    close();
  };

  const handleDefault = () => {
    currentSort.value = { field: '', order: '' };
    emit('update:modelValue', '');
    close();
  };

  const open = () => {
    popup.value?.open('bottom');
    initCurrentSort();
  };

  const close = () => {
    popup.value?.close();
  };

  defineExpose({
    open,
    close
  });
</script>

<style lang="scss" scoped>
  .sort_list {
    @include flex-column;
    padding: 24rpx 32rpx 64rpx 32rpx;
    gap: 24rpx;

    &_item {
      @include flex-align-center;
      @include flex-between;
      height: 82rpx;

      .title {
        font-size: 32rpx;
        color: #000;
      }

      .operate {
        @include flex-align-center;
        gap: 48rpx;

        .desc,
        .asc {
          @include flex-center;
          width: 160rpx;
          height: 64rpx;
          border: 1rpx solid #dcdfe6;
          border-radius: 8rpx;
          color: #000;
          box-sizing: border-box;
          cursor: pointer;

          .icon-Frame {
            margin-right: 16rpx;
          }
        }

        .asc .icon-Frame {
          transform: scaleY(-1);
        }

        .desc.active,
        .asc.active {
          background-color: rgba($color: #6365e0, $alpha: 0.08);
          color: #6365e0;
          border: none;
        }
      }
    }

    &_default {
      width: 100%;
      height: 70rpx;
      background-color: #fff;
      display: block;
      line-height: 70rpx;
      text-align: center;
      font-size: 32rpx;
      cursor: pointer;
      border-radius: 8rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      background-color: #fff;
      color: #000;
      border: 1rpx solid #dcdfe6;
      margin-top: 16rpx;

      &.active {
        background-color: rgba($color: #6365e0, $alpha: 0.08);
        color: #6365e0;
        border: none;
      }
    }
  }
</style>
