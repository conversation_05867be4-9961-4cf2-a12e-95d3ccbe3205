<template>
  <view class="resource_list_item">
    <view class="head_row">
      <view class="left">
        <view class="profile">
          <SafeImage :src="data.headimg_s_url" :gender="data.gender" />
        </view>
        <view class="info">
          <text v-if="data.gender_t">{{ data.gender_t }}</text>
          <text v-if="(data.gender_t && data.nickname) || data.username" class="dot">·</text>
          <text class="nickname text-ellipsis">{{ data.nickname || data.username || '--' }}</text>
        </view>
      </view>
      <view class="right">
        <view v-if="isEdit" class="edit">编辑资料</view>
        <view @click.stop="$emit('select', data)">
          <text class="iconfont" :class="data.select ? 'icon-xuanzhong' : 'icon-weixuanzhong'"></text>
        </view>
      </view>
    </view>

    <view class="content_row">
      <view class="data_row">
        资源ID {{ handleEmptyValue(data.mid) }}
        <template v-if="data.userid > 0">&nbsp;/&nbsp;线上ID {{ data.userid }}</template>
      </view>
      <MobileView :mobile="data.mobile" :id="data.mid" :mobile-info="data.mobile_info" />
      <view class="data_row">
        来源：{{ handleEmptyValue(data.fromcat_t) }}
        <template v-if="data.netway_title">、{{ data.netway_title }}</template>
      </view>
      <view class="data_between_row">
        <view class="data_row left_text">
          身高：{{ handleEmptyValue(data.height) }}
          <template v-if="data.height > 0">cm</template>
        </view>
        <view class="data_row right_text">
          年龄：{{ handleEmptyValue(data.age) }}
          <template v-if="data.age > 0">岁</template>
          <template v-if="data.lunar_t">({{ data.lunar_t }})</template>
        </view>
      </view>
      <view class="data_between_row">
        <view class="data_row left_text">婚况：{{ data.marry_t }}</view>
        <view class="data_row right_text">学历：{{ handleEmptyValue(data.education_t) }}</view>
      </view>
      <view class="data_between_row">
        <view class="data_row left_text">单位：{{ handleEmptyValue(data.companytype_t) }}</view>
        <view class="data_row right_text">收入：{{ handleEmptyValue(data.salary_t) }}</view>
      </view>
    </view>

    <slot name="footer"></slot>
  </view>
</template>

<script setup>
import SafeImage from '@/oeui/new/SafeImage';
import MobileView from '@/oeui/new/MobileView';
import { handleEmptyValue } from '@/utils/hooks.js';

defineProps({
  data: {
    type: Object,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: true
  }
});

defineEmits(['select']);
</script>

<style lang="scss" scoped>
.resource_list_item {
  background: #fff;
  padding: 32rpx 0;
  border-radius: 24rpx;
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .head_row {
    @include flex-center;
    @include flex-between;
    padding-bottom: 24rpx;
    border-bottom: 1rpx solid #f7f7f9;
    margin: 0 24rpx;

    .left {
      @include flex-center;

      .profile {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;

        image {
          width: 100%;
          height: 100%;
          display: block;
          border-radius: 16rpx;
        }
      }

      .info {
        @include flex-center;
        color: #000;
        font-size: 38rpx;
        font-weight: 550;

        .dot {
          font-size: 28rpx;
          color: #8898b6;
          margin: 0 6rpx;
        }

        .nickname {
          display: block;
          max-width: 280rpx;
        }

        .nickname.text-ellipsis {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .right {
      @include flex-center;

      .edit {
        color: #2c5ce1;
        font-size: 28rpx;
        position: relative;
        margin: 0 24rpx;
        padding-right: 24rpx;

        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 2rpx;
          height: 16rpx;
          background-color: #dcdee6;
        }
      }

      .iconfont {
        font-size: 28rpx;
        color: #dcdfe6;
      }

      .icon-xuanzhong {
        color: #6365e0;
      }
    }
  }

  .content_row {
    @include flex-column;
    padding: 32rpx 24rpx;
    gap: 20rpx;
    border-bottom: 1rpx solid #f7f7f9;
    margin-bottom: 24rpx;

    .data_row {
      @include flex-center;
      justify-content: start;
      color: #000;
      font-size: 32rpx;
      line-height: 1;
      flex: 1;
    }

    .data_between_row {
      @include flex-between;
      margin-top: 12rpx;

      .right_text {
        margin-left: 64rpx;
      }
    }
  }

  .footer_row {
    @include flex-center;
    @include flex-between;

    .operate {
      position: relative;
      @include flex-center;
      justify-content: center;
      flex: 1;
      color: #6365e0;
      font-size: 32rpx;
      line-height: 1;

      &:first-child:after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2rpx;
        height: 24rpx;
        background-color: #dcdfe6;
      }

      .icon-fenpei1,
      .icon-touhai {
        margin-right: 12rpx;
        line-height: 1;
      }
    }
  }
}
</style>
