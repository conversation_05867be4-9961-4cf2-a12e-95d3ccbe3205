<template>
  <view class="custom-input-container" :class="{ disabled }">
    <input
      class="custom-input"
      :class="{ 'has-clear': showClear && modelValue }"
      :value="modelValue"
      :type="inputType"
      :placeholder="placeholder"
      :placeholder-style="placeholderStyle"
      :maxlength="maxlength"
      :disabled="disabled"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    <view v-if="showClear && modelValue" class="clear-icon" @click="handleClear">
      <text class="iconfont icon-qingkong-"></text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  maxlength: {
    type: [Number, String],
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showClear: {
    type: Boolean,
    default: true
  },
  align: {
    type: String,
    default: 'right',
    validator: value => ['left', 'center', 'right'].includes(value)
  }
});

const emit = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'clear']);

const inputType = computed(() => {
  return props.type === 'number' ? 'number' : 'text';
});

const placeholderStyle = computed(() => {
  return `color: #C0C4CC; font-size: 32rpx;`;
});

const handleInput = e => {
  let value = e.detail.value;

  // 数字类型处理
  if (props.type === 'number') {
    value = value.replace(/[^\d]/g, '');
  }

  emit('update:modelValue', value);
  emit('input', value);
};

const handleFocus = e => {
  emit('focus', e);
};

const handleBlur = e => {
  emit('blur', e);
};

const handleClear = () => {
  emit('update:modelValue', '');
  emit('clear');
};
</script>

<style lang="scss" scoped>
.custom-input-container {
  position: relative;
  width: 100%;
  @include flex-align-center;

  .custom-input {
    width: 100%;
    height: 44rpx;
    font-size: 32rpx;
    color: #666;
    text-align: v-bind('props.align');

    &.has-clear {
      padding-right: 48rpx;
    }
  }

  ::v-deep .custom-input .uni-input-input {
    font-size: 32rpx !important;
  }

  .clear-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    .icon-qingkong- {
      font-size: 34rpx;
      color: #c0c4cc;
    }
  }
}

.custom-input-container.disabled {
  opacity: 0.5;
}
</style>
