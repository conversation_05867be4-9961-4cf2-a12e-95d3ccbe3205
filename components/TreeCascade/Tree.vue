<template>
  <PopupContainer ref="popup" :title="title" :isDefaultPad="false" :showFooter="!!(shouldShowTree || hasSearchResults)" @confirm="handleConfirm" @close="close">
    <template #operate>
      <text v-if="!searchKeyword && flatNodes.length" class="operate_btn" @click="toggleAll">{{ operateText }}</text>
    </template>

    <template #content>
      <view class="popup_search">
        <EasyInput v-model="searchKeyword" :disabled="!flatNodes.length" :placeholder="placeholder" @search="handleSearch" />
      </view>

      <scroll-view
        class="tree-container"
        :class="{
          empty: !shouldShowTree && !hasSearchResults
        }"
        scroll-y
      >
        <template v-if="shouldShowTree">
          <TreeNode
            v-for="item in flatNodes"
            :key="item.node.isMember ? `member_${item.node.makerid}` : `dept_${item.node.roleid}`"
            :node="item.node"
            :level="item.level"
            @toggle-expand="toggleExpand"
            @select-node="handleSelect"
          />
        </template>

        <template v-else-if="hasSearchResults">
          <SearchResults v-for="node in searchResults" :key="node.roleid" :node="node" @select-node="handleResultsSelect" />
        </template>

        <Empty v-else :description="emptyDescription" />
      </scroll-view>
    </template>
  </PopupContainer>
</template>

<script setup>
  import { reactive, watch, computed, defineProps, defineEmits, defineExpose, ref, onMounted } from 'vue';
  import PopupContainer from '@/oeui/new/PopupContainer';
  import EasyInput from '@/oeui/new/EasyInput';
  import TreeNode from './TreeNode';
  import SearchResults from './SearchResults';
  import Empty from '@/oeui/new/Empty';
  import { getAllCalssMaker, getMakerall } from '@/api/common.js';

  const props = defineProps({
    title: {
      type: String,
      default: '标题'
    },
    placeholder: {
      type: String,
      default: '输入红娘姓名/昵称/红娘ID/手机号'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['confirm']);
  const popup = ref(null);
  const searchKeyword = ref('');
  const selectedNode = ref(null);
  const tempSelectedNode = ref(null);
  const searchResults = ref([]);
  const netpicker = reactive({
    maker: []
  });

  const emptyDescription = computed(() => {
    return searchKeyword.value ? '暂无搜索内容' : '暂无内容';
  });

  const shouldShowTree = computed(() => {
    return !searchKeyword.value && flatNodes.value.length;
  });

  const hasSearchResults = computed(() => {
    return searchKeyword.value && searchResults.value.length;
  });

  // 节点状态管理
  const nodeStateManager = {
    clearSelection(nodes) {
      const stack = [...nodes, ...searchResults.value];
      while (stack.length) {
        const node = stack.pop();
        node.selected = false;
        if (node?.children) stack.push(...node.children);
        if (node?.members) stack.push(...node.members);
      }
    },
    setExpanded(nodes, expanded) {
      const stack = [...nodes];
      while (stack.length) {
        const node = stack.pop();
        if (node.children?.length || node.members?.length) {
          node.expanded = expanded;
          if (node.children) stack.push(...node.children);
        }
      }
    }
  };

  const processData = nodes => {
    return nodes.map(node => {
      const members = (node.maker_list || []).map(member => ({
        ...member,
        isMember: true,
        selected: false,
        name: member.name || member.lastname,
        origin: {
          roleid: node.roleid,
          rolename: node.rolename
        }
      }));

      return {
        ...node,
        expanded: false,
        selected: false,
        children: node.children ? processData(node.children) : [],
        members,
        maker_list: undefined
      };
    });
  };

  const normalizedData = reactive({ list: [] });

  // 数据扁平化
  const flattenTree = (nodes, level = 0) => {
    const result = [];
    const visited = new Set();

    function traverse(node, currentLevel) {
      const nodeId = node.isMember ? `member_${node.makerid}` : `dept_${node.roleid}`;
      if (visited.has(nodeId)) return;
      visited.add(nodeId);
      result.push({ node, level: currentLevel });

      if (node.expanded && !node.isMember) {
        if (node.members) node.members.forEach(m => traverse({ ...m, isMember: true }, currentLevel + 1));
        if (node.children) node.children.forEach(c => traverse(c, currentLevel + 1));
      }
    }

    nodes.forEach(node => traverse(node, level));
    return result;
  };

  const flatNodes = computed(() => flattenTree(normalizedData.list));

  // 递归检查展开状态
  const checkAllExpanded = nodes => {
    return nodes.every(node => {
      if (node.children?.length) {
        return node.expanded && checkAllExpanded(node.children);
      }
      return true;
    });
  };

  const isAllExpandedComputed = computed(() => checkAllExpanded(normalizedData.list));
  const operateText = computed(() => (isAllExpandedComputed.value ? '全部收起' : '全部展开'));

  const toggleExpand = targetNode => {
    if (!targetNode.isMember && (targetNode.children?.length || targetNode.members?.length)) {
      targetNode.expanded = !targetNode.expanded;
    }
  };

  // 选中节点
  const handleSelect = node => {
    nodeStateManager.clearSelection([...normalizedData.list, ...searchResults.value]);
    const target = node.isMember ? findMemberInTree(normalizedData.list, node.makerid) : node;

    if (!target) return;
    target.selected = true;
    tempSelectedNode.value = target;
    selectedNode.value = {
      roleid: target.roleid || target.origin?.roleid || '',
      rolename: target.rolename || target.origin?.rolename || '',
      makerid: target.isMember ? target.makerid : '',
      makername: target.isMember ? target.name || target.lastname : ''
    };
  };

  // 搜索结果选中节点
  const handleResultsSelect = node => {
    nodeStateManager.clearSelection([...normalizedData.list, ...searchResults.value]);
    const target = node;

    if (!target) return;
    target.selected = true;
    target.isMember = true;
    tempSelectedNode.value = target;
    selectedNode.value = {
      roleid: target.roleid || target.origin?.roleid || '',
      rolename: target.rolename || target.origin?.rolename || '',
      makerid: target.makerid || '',
      makername: target.name || ''
    };
  };

  const findMemberInTree = (nodes, makerid) => {
    for (const node of nodes) {
      if (node.members) {
        const member = node.members.find(m => m.makerid === makerid);
        if (member) return member;
      }
      if (node.children) {
        const found = findMemberInTree(node.children, makerid);
        if (found) return found;
      }
    }
    return null;
  };

  // 确认选中节点
  const handleConfirm = () => {
    if (!tempSelectedNode.value) {
      uni.showToast({ title: '至少选择一项', icon: 'none' });
      return;
    }

    const node = tempSelectedNode.value;
    emit('confirm', selectedNode.value);
    close();
  };

  const handleSearch = async () => {
    const keyword = searchKeyword.value.trim();
    if (!keyword) {
      searchResults.value = [];
      return;
    }

    try {
      uni.showLoading();
      const res = await getMakerall({ s_name: keyword, s_sitemk: 1, s_method: 'allot' });
      if (res.ret === 1) {
        searchResults.value = processData(res.result.data);
        nodeStateManager.clearSelection(normalizedData.list);
        nodeStateManager.clearSelection(searchResults.value);
        toggleAll();
      }
    } catch (error) {
      console.error(error);
    } finally {
      uni.hideLoading();
    }
  };

  const open = () => {
    if (props.disabled) return;
    popup.value?.open('bottom');
    searchKeyword.value = '';
  };

  const close = () => {
    if (tempSelectedNode.value) {
      tempSelectedNode.value.selected = false;
    }
    tempSelectedNode.value = null;
    popup.value?.close();
  };

  const toggleAll = () => {
    const newState = !isAllExpandedComputed.value;
    nodeStateManager.setExpanded(normalizedData.list, newState);
  };

  // 获取所有部门+红娘
  const getMakerData = async () => {
    const res = await getAllCalssMaker({ s_name: '' });
    if (res.ret === 1) {
      netpicker.maker = res.result.data;
      normalizedData.list = processData(res.result.data);
      searchResults.value = [];
      toggleAll();
    }
  };

  onMounted(() => {
    getMakerData();
  });

  watch(
    () => searchKeyword.value,
    newVal => {
      if (!newVal.trim()) {
        searchResults.value = [];
        return;
      }

      handleSearch();
    },
    { deep: true }
  );

  defineExpose({ open, close });
</script>

<style lang="scss" scoped>
  .tree-container {
    min-height: 700rpx;
    max-height: 700rpx;
  }

  .tree-container.empty {
    @include flex-center;
    height: 100%;
  }

  .operate_btn {
    position: relative;
    color: #2c5ce1;
    font-size: 32rpx;
    padding-right: 32rpx;
    margin-right: 32rpx;
    cursor: pointer;
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2rpx;
      height: 16rpx;
      background-color: #dcdee6;
    }
  }

  .popup_search {
    margin-bottom: 10rpx;
    padding: 0 32rpx;
  }
</style>
