/*#ifdef H5*/
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-overflow-scrolling: touch;
  // touch-action: none
}

body {
  background: #f7f7f9 !important;
}
/*#endif*/

/*#ifdef MP*/
page {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-overflow-scrolling: touch;
  // touch-action: none
}
/*#endif*/

body {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: 0 auto;
  max-width: 750px;
  background: #f4f5f7;
  font-size: 0.3733rem !important;
  font-family: 'PingFangSC-Semibold', 'Microsoft Yahei', arial, 'Hiragino Sans GB', 'Hiragino Sans GB W3', 宋体, simsun;
  color: $color_font2;
  padding: 0;
  //line-height: 0.586rem;
  height: 100vh;
  overflow: hidden;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: normal;
}
.body_oh {
  height: 100%;
  overflow: hidden;
}
a {
  cursor: pointer;
}
a:link {
  color: #333;
  text-decoration: none;
}
a:visited {
  color: #333;
  text-decoration: none;
}
a:hover {
  color: #333;
  text-decoration: none;
}
a:active {
  color: #333;
  text-decoration: none;
}
input {
  background: none;
  outline: none;
  border: none;
  &:-moz-placeholder {
    color: #999999;
  }
  &::-moz-placeholder {
    color: #999999;
  }
  &:-ms-input-placeholder {
    color: #999999;
  }
  &::-webkit-input-placeholder {
    color: #999999;
  }
}
input:focus {
  border: none;
}
p,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
li,
dl,
dd,
dt,
form {
  list-style: none;
  padding: 0;
  margin: 0;
}
h1,
h2,
h3,
h4,
h5,
h6,
textarea {
  font-family: 'Microsoft Yahei', arial, 'Hiragino Sans GB', 'Hiragino Sans GB W3', 宋体, simsun;
}
img {
  border: none;
  vertical-align: top;
}
.clear {
  clear: both;
}
.clearfix:after {
  content: '';
  display: block;
  clear: both;
}
span,
label,
b,
em,
i {
  font-style: normal;
}
a,
texterea,
input {
  outline: none;
}
img[src=''],
img:not([src]) {
  opacity: 0;
}
textarea,
button,
select {
  outline: none;
  padding: 0;
  appearance: none;
  -webkit-appearance: none;
  margin: 0;
}
textarea {
  line-height: 0.586rem;
}

input::placeholder {
  color: #c0c4cc; /* 占位符文本颜色 */
  font-size: 14px; /* 字体大小 */
  font-style: normal;
}
textarea::placeholder {
  color: #c0c4cc; /* 占位符文本颜色 */
  font-size: 14px; /* 字体大小 */
}
textarea {
  resize: none;
}

.radio {
  display: flex;
  flex-wrap: wrap;
  span {
    width: 22%;
    height: 0.88rem;
    margin-right: 4%;
    margin-bottom: 0.32rem;
    line-height: 0.88rem;
    text-align: center;
    border-radius: 0.16rem;
    background: #f7f7f7;
    font-size: 0.3733rem;
  }
  span:nth-child(4n) {
    margin-right: 0px;
  }
  .current {
    background: $color_main;
    color: #fff;
  }
}

textarea::placeholder {
  font-size: 0.4267rem;
}
