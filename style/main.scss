$color_main: var(--colorMain, #7d68fe);
$color_main_rgba: rgb(99, 101, 224);

$color_font1: #000000;
$color_font2: #373d49;
$color_font3: #8898b6;
$color_jb1: #8f7ffd;
$color_jb2: #7d68ff;
$color_yy1: #6e58ff;
$color_yy2: #5f47ff;
$color_nv: #ff4487;
$color_nan: #2c5ce1;

$comm_assist: #0fc6c2; //辅助色
$comm_assist_rgba: rgb(15, 198, 194);
$comm_warn: var(--commWarn, #ff7d00);

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-align-center {
  display: flex;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

@mixin grid($columns, $gap: 0, $rowGap: 0) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  column-gap: $gap;
  row-gap: $rowGap;
}

.option_list {
  @include grid(3, 24rpx);
  min-height: 9.3333rem;
  max-height: 9.3333rem;
  overflow: auto;
  margin-top: 0.2133rem;
  align-content: start;

  &::-webkit-scrollbar {
    display: none;
  }

  &_item {
    width: 100%;
    height: 82rpx;
    background-color: #fff;
    color: #000;
    display: block;
    line-height: 82rpx;
    text-align: center;
    font-size: 28rpx;
    cursor: pointer;
    border: 1rpx solid #f7f7f9;
    border-radius: 16rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .item-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &:active {
      background: #f7f7f9;
    }

    &.selected {
      background: #6365e0;
      border: none;
      color: #fff;
    }
  }
}

::v-deep .uni-popup.bottom {
  z-index: 9999 !important;
}

::v-deep .uni-popup.right {
  z-index: 9999 !important;
}

.date_filter,
.type_filter {
  ::v-deep .uni-section-header {
    display: none;
  }

  ::v-deep .uni-stat-box {
    z-index: 99;
  }

  ::v-deep .uni-select {
    border: none;
    height: 54rpx;
  }

  ::v-deep .uni-select .uni-icons {
    display: none;
  }

  ::v-deep .uni-select__input-text {
    display: none;
  }

  ::v-deep .uni-select__selector-item {
    justify-content: center;
  }
}

::v-deep .uni-drawer__content.uni-drawer--right.uni-drawer__content--visible {
  width: 100vw !important;
}

::v-deep .uni-switch-input {
  width: 44px;
  height: 24px;
  margin: 0;

  &::before {
    width: 44px;
    height: 24px;
    background-color: #c0c4cc;
  }
  &::after {
    width: 20px;
    height: 20px;
    top: 3rpx;
    left: 2rpx;
  }
}
