.color_2a2 {
  color: $color_font1;
}
.color_green {
  color: #15ce7b !important;
}
.color_red {
  color: #ed1616 !important;
}
.color_main {
  color: $color_main !important;
}
.fz10 {
  font-size: 0.2667rem;
}
.fz12 {
  font-size: 0.32rem !important;
}
.fz14 {
  font-size: 0.3733rem;
}
.fz16 {
  font-size: 0.4267rem !important;
}
.fz18 {
  font-size: 0.48rem !important;
}
.fz20 {
  font-size: 0.5333rem;
}
.p12 {
  padding: 0.32rem;
}
.pb12 {
  padding-bottom: 0.32rem;
}
.plr16 {
  padding-left: 0.4267rem;
  padding-right: 0.4267rem;
}
.pb16 {
  padding-bottom: 0.4267rem;
}
.br8 {
  border-radius: 16rpx;
}
.br12 {
  border-radius: 0.32rem;
}

.color_font1 {
  color: #000000;
}
.color_font2 {
  color: #31293b;
}
.color_font3 {
  color: #8898b6;
}
.color_6 {
  color: #666 !important;
}
.mt12 {
  margin-top: 0.32rem;
}
.mt16 {
  margin-top: 32rpx;
}
.mb12 {
  margin-bottom: 0.32rem;
}
.mb16 {
  margin-bottom: 0.4267rem;
}
.mb16i {
  margin-bottom: 0.4267rem !important;
}
.mt8 {
  margin-top: 0.2133rem !important;
}
.ml8 {
  margin-left: 0.2133rem;
}
.mb8 {
  margin-bottom: 0.2133rem;
}
.mr8 {
  margin-right: 0.2133rem;
}
.pr {
  position: relative;
}
.bo_bottom {
  border-bottom: 1px solid #f7f7f7;
}
.ob_c {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.b_bottom {
  border-bottom: 1px solid #f7f7f9;
}
.fb {
  font-weight: bold;
}
.oh {
  overflow: hidden;
}
.ws {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.color_c0c {
  color: #c0c4cc;
}
.vam {
  vertical-align: middle !important;
}

.flex {
  display: flex;
}
.flex_v {
  flex-direction: column;
}
.flex_warp {
  flex-wrap: wrap;
}
.flex_dc {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex_ac {
  align-items: center;
}
.flex_jsb {
  justify-content: space-between;
}
.flex_s {
  flex-shrink: 0;
}
.flex_1 {
  flex: 1;
}
.flex_el {
  justify-content: space-evenly;
}
.w100 {
  width: 100%;
}
.w50 {
  width: 50%;
}
.ws {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.fw5 {
  font-weight: 500;
}
.fn_i {
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: normal;
}
.pf {
  position: fixed;
}
.pa {
  position: absolute;
}
.pr {
  position: relative;
}
.w100per {
  width: 100%;
}
.bg_f {
  background: #fff;
}
.h50 {
  height: 1.3333rem;
}
.h20 {
  height: 0.5333rem;
}
.h60 {
  height: 1.6rem;
}
.h70 {
  height: 1.8667rem;
}
.h80 {
  height: 2.1333rem;
}
.h44 {
  height: 1.1733rem;
}

.lbr {
  left: 0;
  right: 0;
  bottom: 0;
}
.bsb {
  box-sizing: border-box;
}
input,
input:focus,
textarea,
textarea:focus,
select,
select:focus {
  font-size: 0.3733rem !important;
}

.clear {
  clear: both;
}

.color_success {
  color: #00b42a;
}
.color_warn {
  color: #ff7d00;
}
.color_error {
  color: #f53f3f;
}
.color_assist {
  color: #0fc6c2;
}
.btn_warn {
  border-radius: 10rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 5rpx 16rpx;
  color: #ff7d00;
  background: rgba(255, 125, 0, 0.12);
}
.btn_info {
  border-radius: 10rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 5rpx 16rpx;
  color: #8898b6;
  background: #f7f7f9;
}
.btn_green {
  border-radius: 10rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 5rpx 16rpx;
  color: #00b42a;
  background: rgba(0, 180, 42, 0.12);
}
.btn_red {
  border-radius: 10rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 5rpx 16rpx;
  color: #f53f3f;
  background: rgba(245, 63, 63, 0.12);
}
.btn_per {
  border-radius: 24rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 14rpx 24rpx;
  color: #6365e0;
  background: #bbbcfa14;
}
.font-info {
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: 0px;

  /* 三级标色 */
  color: #8898b6;
}
.itemico {
  font-size: 0.3733rem;
  color: #666;
  margin-left: 0.2133rem;
}
.img24 {
  width: 48rpx;
  height: 48rpx;
}
.img44 {
  width: 88rpx;
  height: 88rpx;
}
