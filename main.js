import App from './App'
import CustomNavBar from '@/oeui/new/CustomNavBar'
import { showModal } from '@/oeui/new/Modal/modal'

// #ifdef H5
window.wx = {}
// #endif


// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import store from './store'
import 'lib-flexible/flexible';


Vue.prototype.$store = store

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import Vuex from "vuex";
import store from './store'
import power from './utils/power'

export function createApp() {
  const app = createSSRApp(App)
  app.component('CustomNavBar', CustomNavBar)
  app.config.globalProperties.$showModal = showModal
  uni.showGlobalModal = showModal
  app.use(store)
  app.use(power)
  return {
    app,
    Vuex
  }
}
// #endif