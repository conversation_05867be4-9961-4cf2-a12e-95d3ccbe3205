<template>
  <scroll-view scroll-y :scroll-top="scrollTop" @scroll="onScroll" :style="{ height: containerHeight }" class="virtual-container">
    <view
      class="virtual-list"
      :style="{
        padding: `${paddingTop}px 0 ${paddingBottom}px`,
        boxSizing: 'border-box'
      }"
    >
      <view v-for="(item, index) in virtualData" :key="index" :class="`day-${item.year}-${item.month + 1}`" class="virtual-item">
        <slot name="item" :item="item"></slot>
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';

  const props = defineProps({
    dataList: Array,
    itemHeight: {
      type: Number,
      default: 380
    },
    containerHeight: {
      type: String,
      default: '100vh'
    }
  });

  const scrollTop = ref(0);
  const viewHeight = ref(0);

  // 计算可视区域索引
  const { start, end } = useVisibleRange(scrollTop, viewHeight, props);
  const paddingTop = computed(() => start.value * props.itemHeight);
  const paddingBottom = computed(() => (props.dataList.length - end.value) * props.itemHeight);
  const virtualData = computed(() => props.dataList.slice(start.value, end.value));

  // 记录滚动条位置
  const onScroll = e => {
    scrollTop.value = e.detail.scrollTop;
  };

  // 初始化获取容器高度
  onMounted(() => {
    uni
      .createSelectorQuery()
      .select('.virtual-container')
      .boundingClientRect(res => {
        viewHeight.value = res.height;
      })
      .exec();
  });

  // 可视范围计算逻辑
  function useVisibleRange(scrollTop, viewHeight, props) {
    const start = computed(() => {
      return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - 2);
    });

    const end = computed(() => {
      return Math.min(props.dataList.length, Math.ceil((scrollTop.value + viewHeight.value) / props.itemHeight) + 2);
    });

    return { start, end };
  }

  defineExpose({
    scrollTo: position => {
      scrollTop.value = position;
    }
  });
</script>

<style lang="scss" scoped>
  .virtual-container {
    position: relative;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .virtual-list {
      width: 100%;
      position: relative;
      will-change: transform;
    }
  }
</style>
