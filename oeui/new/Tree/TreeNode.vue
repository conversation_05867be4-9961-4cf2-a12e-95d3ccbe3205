<template>
  <view class="node" :class="{ selected: node.selected }" :style="{ paddingLeft }" @click="handleClick">
    <view class="label" :class="isLeaf && 'label_option'">{{ node.rolename || node.name }}</view>
    <view v-if="!isLeaf" class="arrow">
      <text class="iconfont icon-zhankai-011" :class="node.expanded ? 'expanded_icon' : 'collapsed_icon'"></text>
    </view>
    <view v-if="isLeaf && node.selected" class="selected-icon">
      <text class="iconfont icon-xuanzhong"></text>
    </view>
  </view>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  });

  const emit = defineEmits(['toggle-expand', 'select-node']);

  const paddingLeft = computed(() => `${props.level * 32}rpx`);
  const isLeaf = computed(() => {
    return !props.node.children || props.node.children.length === 0;
  });

  const handleClick = () => {
    if (isLeaf.value) {
      emit('select-node', props.node);
    } else {
      emit('toggle-expand', props.node);
    }
  };
</script>

<style lang="scss" scoped>
  .node {
    display: flex;
    align-items: center;
    height: 86rpx;
    padding: 0 40rpx;

    &.selected {
      background: #f3f3fd;
      .label.label_option {
        color: #6365e0 !important;
      }
    }

    .label.label_option {
      color: #000 !important;
    }

    .label {
      flex: 1;
      font-size: 32rpx;
      color: #666;
      user-select: none;
      text-align: left;
      padding-left: 32rpx;
    }

    .arrow {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      will-change: transform;
      .iconfont {
        font-size: 36rpx;
        color: #666;
        -webkit-transition: transform 0.3s ease-in-out;
        transition: transform 0.3s ease-in-out;
      }
      .expanded_icon {
        transform: rotate(180deg);
      }
      .collapsed_icon {
        transform: rotate(0deg);
      }
    }

    .selected-icon {
      .icon-xuanzhong {
        font-size: 32rpx;
        color: #6365e0;
      }
    }
  }
</style>
