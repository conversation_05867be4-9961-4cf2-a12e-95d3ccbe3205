<template>
  <view class="node" :class="{ selected: node.selected }" @click="handleClick">
    <view class="label">
      {{ node.name || node.rolename }}
      <text class="parent" v-if="node.parentRolename">[{{ node.parentRolename }}]</text>
    </view>
    <view v-if="node.selected" class="selected-icon">
      <text class="iconfont icon-xuanzhong"></text>
    </view>
    <view v-else class="selected-icon">
      <text class="iconfont icon-weixuanzhong"></text>
    </view>
  </view>
</template>

<script setup>
  const props = defineProps({
    node: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits(['select-node']);

  const handleClick = () => {
    emit('select-node', props.node);
  };
</script>

<style lang="scss" scoped>
  .node {
    display: flex;
    align-items: center;
    height: 86rpx;
    padding: 0 40rpx;

    &.selected {
      background: #f3f3fd;
      .label,
      .parent {
        color: #6365e0 !important;
      }
    }

    .label {
      flex: 1;
      font-size: 32rpx;
      color: #000;
      user-select: none;
      text-align: left;
      display: flex;
      align-items: center;

      .parent {
        color: #666;
        margin-left: 8rpx;
      }
    }

    .selected-icon {
      .icon-xuanzhong {
        font-size: 32rpx;
        color: #6365e0;
      }

      .icon-weixuanzhong {
        font-size: 32rpx;
        color: #dcdfe6;
      }
    }
  }
</style>
