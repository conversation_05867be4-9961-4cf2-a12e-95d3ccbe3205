<template>
  <PopupContainer ref="popup" :title="title" :isDefaultPad="false" @confirm="handleConfirm" @close="close">
    <template #content>
      <picker-view class="picker-view" indicator-class="picker-indicator-class" :value="pickerValue" @change="handlePickerChange">
        <picker-view-column class="item" v-for="(column, colIndex) in columns" :key="colIndex">
          <view class="picker-item" v-for="item in getColumnOptions(colIndex)" :key="item.value">
            {{ item.text }}
          </view>
        </picker-view-column>
      </picker-view>
    </template>
  </PopupContainer>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineExpose, watch, onMounted } from 'vue';
import PopupContainer from './PopupContainer.vue';

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  options: {
    type: [Array, Array.of(Array)],
    default: () => []
  },
  modelValue: {
    type: [String, Number, Array],
    default: () => ''
  },
  columns: {
    type: Number,
    default: 1
  },
  linkage: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'change']);

const popup = ref(null);
const searchKeyword = ref('');
const selectedValue = ref([]);
const pickerValue = ref([]);
const isInitialized = ref(false);

// 判断是否是单列模式
const isSingleColumn = computed(() => {
  return props.columns === 1 && !Array.isArray(props.modelValue) && (props.options.length === 0 || !Array.isArray(props.options[0]));
});

// 获取过滤后的选项（仅单列模式使用）
const filteredOptions = computed(() => {
  if (!isSingleColumn.value) return [];

  const keyword = searchKeyword.value.trim().toLowerCase();
  if (!keyword) return props.options;
  return props.options.filter(item => item.text.toLowerCase().includes(keyword));
});

// 获取指定列的选项
const getColumnOptions = colIndex => {
  if (isSingleColumn.value) {
    return isInitialized.value ? filteredOptions.value : props.options;
  }

  if (colIndex === 0) {
    return Array.isArray(props.options[0]) ? props.options[0] : props.options;
  }

  if (props.linkage) {
    return props.linkage(colIndex, selectedValue.value.slice(0, colIndex)) || [];
  }

  return props.options[colIndex] || [];
};

// 初始化选择值
const initSelectedValue = () => {
  if (isSingleColumn.value) {
    const modelValue = props.modelValue;
    const defaultValue = props.options.length > 0 ? props.options[0].value : '';
    const matchedOption = props.options.find(item => String(item.value) === String(modelValue));
    selectedValue.value = [matchedOption ? matchedOption.value : defaultValue];
  } else {
    if (Array.isArray(props.modelValue) && props.modelValue.length === props.columns) {
      selectedValue.value = props.modelValue.map((val, index) => {
        const colOptions = getColumnOptions(index);
        const matchedOption = colOptions.find(item => String(item.value) === String(val));
        return matchedOption ? matchedOption.value : colOptions.length > 0 ? colOptions[0].value : '';
      });
    } else {
      selectedValue.value = Array(props.columns)
        .fill('')
        .map((_, i) => {
          const colOptions = getColumnOptions(i);
          return colOptions.length > 0 ? colOptions[0].value : '';
        });
    }
  }
};

// 初始化选择索引
const initPickerValue = () => {
  pickerValue.value = selectedValue.value.map((val, colIndex) => {
    const options = getColumnOptions(colIndex);
    const index = options.findIndex(item => item.value === val);
    return index >= 0 ? index : 0;
  });
};

// 更新选择值
const updateSelectedValue = (colIndex, value) => {
  selectedValue.value[colIndex] = value;

  // 如果是多列且有联动关系，需要重置后续列的选择
  if (!isSingleColumn.value && props.linkage) {
    for (let i = colIndex + 1; i < props.columns; i++) {
      const nextOptions = getColumnOptions(i);
      selectedValue.value[i] = nextOptions.length > 0 ? nextOptions[0].value : '';
    }
  }
};

// 初始化组件
const initialize = () => {
  initSelectedValue();
  initPickerValue();
  isInitialized.value = true;
};

onMounted(() => {
  initialize();
});

watch(
  () => props.modelValue,
  newVal => {
    if (isSingleColumn.value) {
      selectedValue.value = [newVal || (props.options.length > 0 ? props.options[0].value : '')];
    } else if (Array.isArray(newVal) && newVal.length === props.columns) {
      selectedValue.value = [...newVal];
    }
    initPickerValue();
  },
  { immediate: true }
);

watch(
  () => props.options,
  () => {
    initSelectedValue();
    initPickerValue();
  },
  { deep: true }
);

// 处理选择变化
const handlePickerChange = e => {
  const values = e.detail.value;

  // 更新选中值
  values.forEach((index, colIndex) => {
    const options = getColumnOptions(colIndex);
    if (index >= 0 && index < options.length) {
      updateSelectedValue(colIndex, options[index].value);
    }
  });

  // 两列区间选择处理
  // if (!isSingleColumn.value && props.columns >= 2) {
  //   const firstColOptions = getColumnOptions(0);
  //   const secondColOptions = getColumnOptions(1);

  //   if (firstColOptions.length > 0 && secondColOptions.length > 0) {
  //     const firstValue = Number(selectedValue.value[0]);

  //     // 确保第二列 >= 第一列
  //     if (!isNaN(firstValue)) {
  //       let newSecondIndex = -1;
  //       let newSecondValue = firstValue + 1;

  //       // 找firstValue+1
  //       newSecondIndex = secondColOptions.findIndex(item => Number(item.value) === newSecondValue);

  //       // 找不到则找>firstValue的最小值
  //       if (newSecondIndex === -1) {
  //         const minGreater = secondColOptions.reduce((min, item) => {
  //           const val = Number(item.value);
  //           return val > firstValue && val < min ? val : min;
  //         }, Infinity);

  //         if (minGreater !== Infinity) {
  //           newSecondValue = minGreater;
  //           newSecondIndex = secondColOptions.findIndex(item => Number(item.value) === minGreater);
  //         }
  //       }

  //       //  找不到则选firstValue
  //       if (newSecondIndex === -1) {
  //         newSecondValue = firstValue;
  //         newSecondIndex = secondColOptions.findIndex(item => Number(item.value) === firstValue);
  //       }

  //       // 更新第二列
  //       if (newSecondIndex >= 0) {
  //         selectedValue.value[1] = secondColOptions[newSecondIndex].value;
  //         values[1] = newSecondIndex;
  //       }
  //     }
  //   }
  // }

  pickerValue.value = values;

  // 触发事件
  const selectedItems = selectedValue.value.map((val, i) => {
    const options = getColumnOptions(i);
    return options.find(item => item.value === val);
  });

  emit('change', isSingleColumn.value ? selectedItems[0] : selectedItems);
};

// 获取选中的项
const getSelectedItems = () => {
  return selectedValue.value.map((val, colIndex) => {
    const options = getColumnOptions(colIndex);
    return options.find(item => item.value === val) || (options.length > 0 ? options[0] : null);
  });
};

// 确认选择
const handleConfirm = () => {
  const selectedItems = getSelectedItems();

  if (selectedItems.some(item => !item)) {
    uni.showToast({ title: '请完成所有选项选择', icon: 'none' });
    return;
  }

  if (isSingleColumn.value) {
    emit('update:modelValue', selectedItems[0].value);
    emit('confirm', selectedItems[0]);
  } else {
    emit(
      'update:modelValue',
      selectedItems.map(item => item.value)
    );
    emit('confirm', selectedItems);
  }

  close();
};

const open = () => {
  popup.value?.open('bottom');
  searchKeyword.value = '';
  initialize();
};

const close = () => {
  popup.value?.close();
};

defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.picker-view {
  width: 100%;
  height: 600rpx;

  .picker-indicator-class {
    height: 88rpx;
    background-color: #f7f7f9;
    z-index: 0;
  }

  .picker-indicator-class::after {
    border: none;
  }

  .picker-indicator-class::before {
    border: none;
  }

  .picker-item {
    line-height: 88rpx;
    font-size: 32rpx;
    color: #000;
    text-align: center;
    padding: 0 24rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
</style>
