<template>
  <view class="textarea_box" :class="{ disabled, back: isBackground }">
    <textarea
      class="textarea_input"
      :disabled="disabled"
      :placeholder-style="placeholderStyle"
      :placeholder="placeholder"
      :maxlength="max"
      :auto-height="autoHeight"
      :fixed="fixed"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :cursor-spacing="cursorSpacing"
      v-model="inputValue"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
      @confirm="handleConfirm"
    />
    <view class="counter_box" v-if="showCounter">
      <text class="counter_text">{{ currentCount }}/{{ max }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: '请输入'
  },
  placeholderStyle: {
    type: String,
    default: 'color: #C0C4CC; font-size:28rpx;'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  max: {
    type: Number,
    default: 200
  },
  showCounter: {
    type: Boolean,
    default: true
  },
  autoHeight: {
    type: Boolean,
    default: true
  },
  fixed: {
    type: Boolean,
    default: false
  },
  showConfirmBar: {
    type: Boolean,
    default: true
  },
  adjustPosition: {
    type: Boolean,
    default: true
  },
  cursorSpacing: {
    type: Number,
    default: 0
  },
  isBackground: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'input', 'blur', 'focus', 'confirm']);

const inputValue = ref(props.modelValue || '');

const currentCount = computed(() => {
  return inputValue.value.length;
});

watch(
  () => props.modelValue,
  newVal => {
    inputValue.value = newVal || '';
  }
);

const handleInput = event => {
  const value = event.detail.value;
  inputValue.value = value;
  emit('input', value);
  emit('update:modelValue', value);
};

const handleBlur = event => {
  emit('blur', event);
};

const handleFocus = event => {
  emit('focus', event);
};

const handleConfirm = event => {
  emit('confirm', event);
};
</script>

<style lang="scss" scoped>
.textarea_box.back {
  background: #f7f7f9;
  border: none;
}

.textarea_box {
  position: relative;
  border-radius: 16rpx;
  padding: 24rpx;
  min-height: 150rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  border: 1rpx solid #dcdfe6;
  background: #fff;

  .textarea_input {
    width: 100%;
    min-height: 100rpx;
    font-size: 28rpx;
    line-height: 1.5;
    color: #333;
  }

  ::v-deep .uni-textarea-textarea {
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .counter_box {
    position: absolute;
    right: 24rpx;
    bottom: 8rpx;
    text-align: right;

    .counter_text {
      font-size: 24rpx;
      color: #8898b6;
    }
  }
}

.textarea_box.disabled {
  opacity: 0.5;
}
</style>
