<template>
  <view class="tabs-container">
    <view class="tabs-slider" :style="sliderStyle"></view>
    <view v-for="(tab, index) in tabs" :key="index" :id="`tab_${index}`" class="tabs-item" :class="{ active: activeIndex === index }" @click="handleTabClick(index)">
      {{ tab }}
    </view>
  </view>
</template>

<script setup>
  import { ref, watch, onMounted } from 'vue';

  const props = defineProps({
    tabs: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Number,
      default: 0
    }
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  const activeIndex = ref(props.modelValue);
  const sliderStyle = ref({
    left: '0px',
    width: '0px'
  });

  const getTabRect = index => {
    return new Promise(resolve => {
      uni
        .createSelectorQuery()
        .select(`#tab_${index}`)
        .boundingClientRect(res => resolve(res))
        .exec();
    });
  };

  const updateSliderPosition = async () => {
    try {
      const rect = await getTabRect(activeIndex.value);
      if (rect) {
        sliderStyle.value = {
          left: `${rect.left - 16}px`, // 16是该元素距离左边的间距
          width: `${rect.width}px`
        };
      }
    } catch (e) {
      console.error(e);
    }
  };

  const handleTabClick = index => {
    if (index === activeIndex.value) return;
    activeIndex.value = index;
    emit('update:modelValue', index);
    emit('change', index);
  };

  watch(
    () => props.modelValue,
    newVal => {
      activeIndex.value = newVal;
      updateSliderPosition();
    }
  );

  onMounted(() => {
    updateSliderPosition();
  });
</script>

<style lang="scss" scoped>
  .tabs-container {
    position: relative;
    @include flex-center;
    width: 100%;
    height: 72rpx;
    border-radius: 64rpx;
    background: #f7f7f9;

    .tabs-slider {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      height: 64rpx;
      background: #6365e0;
      border-radius: 64rpx;
      box-shadow: 0 4rpx 12rpx rgba(82, 100, 132, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 0;
    }

    .tabs-item {
      @include flex-center;
      height: 64rpx;
      flex: 1;
      border-radius: 64rpx;
      color: #8898b6;
      font-size: 28rpx;
      z-index: 20;
      transition: color 0.3s;

      &.active {
        color: #fff;
      }
    }
  }
</style>
