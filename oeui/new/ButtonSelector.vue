<template>
  <view class="button-selector">
    <!-- 单选模式 -->
    <template v-if="!multiple">
      <view class="button-row">
        <view v-for="item in options" :key="item.value" class="button" :class="{ active: String(selectedValue) === String(item.value) }" @click="handleSelect(item)">
          {{ item.text }}
        </view>
      </view>
    </template>

    <!-- 多选模式 -->
    <template v-else>
      <view class="button-row">
        <view v-for="item in options" :key="item.value" class="button" :class="{ active: isSelected(item) }" @click="handleSelect(item)">
          {{ item.text }}
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  options: {
    type: Array,
    required: true,
    default: () => [],
    validator: value => {
      return value.every(item => item.text && item.value !== undefined);
    }
  },
  multiple: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: [String, Number, Array, String],
    default: () => ''
  },
  maxSelect: {
    type: Number,
    default: 0
  },
  showCount: {
    type: Boolean,
    default: true
  },
  buttonClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 单选选中的值
const selectedValue = ref(props.multiple ? '' : props.modelValue);

// 多选选中的值
const selectedValues = ref('');

// 将字符串转换为数组
const stringToArray = str => {
  if (!str) return [];
  return str.split(',').filter(v => v !== '');
};

// 将数组转换为字符串
const arrayToString = arr => {
  return arr.map(item => item.value).join(',');
};

// 初始化选中值
const initSelectedValues = () => {
  if (props.multiple) {
    if (Array.isArray(props.modelValue)) {
      selectedValues.value = arrayToString(props.modelValue);
    } else {
      selectedValues.value = props.modelValue || '';
    }
  }
};

// 检查是否选中(多选)
const isSelected = item => {
  const values = stringToArray(selectedValues.value);
  return values.includes(String(item.value));
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  newVal => {
    if (props.multiple) {
      if (Array.isArray(newVal)) {
        selectedValues.value = arrayToString(newVal);
      } else {
        selectedValues.value = newVal || '';
      }
    } else {
      selectedValue.value = newVal || '';
    }
  },
  { immediate: true }
);

// 选择处理
const handleSelect = item => {
  if (props.multiple) {
    let values = stringToArray(selectedValues.value);
    const valueStr = String(item.value);

    if (values.includes(valueStr)) {
      // 已选中则移除
      values = values.filter(v => v !== valueStr);
    } else {
      // 未选中则添加
      if (props.maxSelect > 0 && values.length >= props.maxSelect) {
        uni.showToast({
          title: `最多选择${props.maxSelect}项`,
          icon: 'none'
        });
        return;
      }
      values.push(valueStr);
    }

    selectedValues.value = values.join(',');
    emitChange(selectedValues.value);
  } else {
    selectedValue.value = selectedValue.value === item.value ? '' : item.value;
    emitChange(selectedValue.value);
  }
};

// 触发change事件
const emitChange = value => {
  emit('update:modelValue', value);
  emit('change', value);
};

initSelectedValues();
</script>

<style lang="scss" scoped>
.button-selector {
  width: 100%;
}

.button-row {
  display: flex;
  flex-wrap: wrap;
  @include flex-align-center;
  gap: 24rpx;
}

.button {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  background-color: #f7f7f9;
  color: #000;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.1s;
  box-sizing: border-box;
  border: 1rpx solid transparent;

  &.active {
    background-color: #f3f3fd;
    color: #6365e0;
    border: 1rpx solid #6365e0;
  }
}
</style>
