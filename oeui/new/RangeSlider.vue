<template>
  <view class="range-slider" :style="`width:${width}rpx;height:${height}rpx`">
    <view class="range-bar" :style="`height:${barHeight}rpx`">
      <view class="range-bar-bg" :style="`background-color:${backgroundColor}; height: ${barHeight}rpx`"></view>
      <view
        class="range-bar-progress"
        :style="{
          left: `${progressBarLeft}rpx`,
          backgroundColor: activeColor,
          width: `${progressBarWidth}rpx`,
          height: `${barHeight - 8}rpx`,
          top: `4rpx`
        }"
      ></view>
    </view>
    <view
      class="block"
      :class="{ active: isMinActive }"
      :style="`width:${blockSize}rpx;height:${blockSize}rpx;left:${minBlockLeft}rpx;`"
      @touchstart="onBlockTouchStart"
      @touchmove.stop="onBlockTouchMove"
      @touchend="onBlockTouchEnd"
      :data-left="minBlockLeft"
      data-tag="minBlock"
    >
      <view class="bg">
        <view class="dot"></view>
      </view>
    </view>
    <!-- left +4rpx是为了挡住滑块颜色 -->
    <view
      class="block"
      :class="{ active: isMaxActive }"
      :style="`width:${blockSize}rpx;height:${blockSize}rpx;left:${maxBlockLeft + 4}rpx;`"
      @touchstart="onBlockTouchStart"
      @touchmove.stop="onBlockTouchMove"
      @touchend="onBlockTouchEnd"
      :data-left="maxBlockLeft + 4"
      data-tag="maxBlock"
    >
      <view class="bg">
        <view class="dot"></view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, watch, onMounted, defineExpose } from 'vue';

  const props = defineProps({
    // 组件宽度
    width: {
      type: Number,
      default: 750
    },
    // 组件高度
    height: {
      type: Number,
      default: 100
    },
    // 滑块大小
    blockSize: {
      type: Number,
      default: 36
    },
    // 区间进度条高度
    barHeight: {
      type: Number,
      default: 40
    },
    // 背景条颜色
    backgroundColor: {
      type: String,
      default: '#F7F7F9'
    },
    // 已选择的颜色
    activeColor: {
      type: String,
      default: '#6365E0'
    },
    // 最小值
    min: {
      type: Number,
      default: 0
    },
    // 最大值
    max: {
      type: Number,
      default: 100
    },
    // 设置初始值
    values: {
      type: Array,
      default: () => [0, 100]
    },
    // 步长值
    step: {
      type: Number,
      default: 1
    },
    // live模式，是否动态更新
    liveMode: {
      type: Boolean,
      default: true
    }
  });

  const emit = defineEmits(['rangechange']);

  const _windowWidth = uni.getSystemInfoSync().windowWidth;

  // 响应式数据
  const isMinActive = ref(false);
  const isMaxActive = ref(false);

  // 平台差异处理
  const MAX_LENGTH = ref(700); // 默认值
  const maxBlockLeft = ref(350); // 默认值

  // #ifdef H5
  MAX_LENGTH.value = 294;
  maxBlockLeft.value = 300;
  // #endif

  const minBlockLeft = ref(0);
  const progressBarLeft = ref(0);
  const progressBarWidth = ref(350);

  const originalMinValue = ref(0);
  const originalMaxValue = ref(0);

  // 临时变量
  let blockDownX = 0;
  let blockLeft = 0;
  let curBlock = '';

  // 方法
  const pad = (num, n) => {
    return Array(n - ('' + num).length + 1).join('0') + num;
  };

  const pxToRpx = px => {
    return (750 * px) / _windowWidth;
  };

  const onBlockTouchStart = e => {
    const tag = e.currentTarget.dataset.tag;
    if (tag === 'minBlock' || tag === 'maxBlock') {
      isMinActive.value = tag === 'minBlock';
      isMaxActive.value = tag === 'maxBlock';

      if (e.hasOwnProperty('changedTouches')) {
        blockDownX = e.changedTouches[0].pageX;
      } else {
        blockDownX = e.pageX;
      }

      // #ifdef H5
      blockLeft = parseFloat(e.currentTarget.dataset.left);
      // #endif
      // #ifndef H5
      blockLeft = e.currentTarget.dataset.left;
      // #endif

      curBlock = e.currentTarget.dataset.tag;
    }
  };

  const onBlockTouchMove = e => {
    const tag = e.currentTarget.dataset.tag;
    if (tag === 'minBlock' || tag === 'maxBlock') {
      const values = calculateValues(e);
      refreshProgressBar(values[2], values[3]);
      refreshBlock(values[2], values[3]);

      const eventDetail = {
        minValue: formatNumber(values[0], props.step),
        maxValue: formatNumber(values[1], props.step),
        fromUser: true,
        originalValue: {
          minValue: values[0],
          maxValue: values[1]
        }
      };

      originalMinValue.value = values[0];
      originalMaxValue.value = values[1];

      if (props.liveMode) emit('rangechange', eventDetail);
    }
  };

  const onBlockTouchEnd = e => {
    const tag = e.currentTarget.dataset.tag;
    isMinActive.value = false;
    isMaxActive.value = false;
    if (tag === 'minBlock' || tag === 'maxBlock') {
      const values = calculateValues(e.changedTouches[0]);
      refreshProgressBar(values[2], values[3]);
      refreshBlock(values[2], values[3]);

      const eventDetail = {
        minValue: formatNumber(values[0], props.step),
        maxValue: formatNumber(values[1], props.step),
        fromUser: true,
        originalValue: {
          minValue: values[0],
          maxValue: values[1]
        }
      };

      originalMinValue.value = values[0];
      originalMaxValue.value = values[1];
      emit('rangechange', eventDetail);
    }
  };

  const isValuesValid = values => {
    return values != null && values !== undefined && values.length === 2;
  };

  const calculateValues = e => {
    let pageX = e.pageX;
    if (e.hasOwnProperty('changedTouches')) {
      pageX = e.changedTouches[0].pageX;
    }

    const moveLength = pageX - blockDownX;
    let left = blockLeft + pxToRpx(moveLength);
    left = Math.max(0, left);
    left = Math.min(left, MAX_LENGTH.value);

    let minBlockLeftValue = minBlockLeft.value;
    let maxBlockLeftValue = maxBlockLeft.value;

    if (curBlock === 'minBlock') {
      minBlockLeftValue = left;
    } else {
      maxBlockLeftValue = left;
    }

    const range = props.max - props.min;
    const minLeft = Math.min(minBlockLeftValue, maxBlockLeftValue);
    const maxLeft = Math.max(minBlockLeftValue, maxBlockLeftValue);
    const minValue = (minLeft / MAX_LENGTH.value) * range + props.min;
    const maxValue = (maxLeft / MAX_LENGTH.value) * range + props.min;

    return [minValue, maxValue, minLeft, maxLeft];
  };

  const calculateBlockLeft = (minValue, maxValue) => {
    const range = props.max - props.min;
    const minLeft = ((minValue - props.min) / range) * MAX_LENGTH.value;
    const maxLeft = ((maxValue - props.min) / range) * MAX_LENGTH.value;
    return [minLeft, maxLeft];
  };

  const refreshProgressBar = (minBlockLeftValue, maxBlockLeftValue) => {
    progressBarLeft.value = minBlockLeftValue + props.blockSize / 2;
    progressBarWidth.value = Math.abs(maxBlockLeftValue - minBlockLeftValue);
  };

  const refreshBlock = (minBlockLeftValue, maxBlockLeftValue) => {
    minBlockLeft.value = minBlockLeftValue;
    maxBlockLeft.value = maxBlockLeftValue;
  };

  const formatNumber = (num, step) => {
    const stepStr = '' + step;
    const index = stepStr.indexOf('.');
    const len = index > -1 ? stepStr.length - index - 1 : 0;
    const offestNum = parseInt(1 + Array(('' + len).length + 1).join('0')) * 0.1;
    const tmpNum = num * offestNum;
    return ((parseInt(tmpNum / step + (step > 1 ? 1 : step) * 0.5) * step) / offestNum).toFixed(len);
  };

  const refresh = () => {
    MAX_LENGTH.value = props.width - props.blockSize;
    maxBlockLeft.value = MAX_LENGTH.value;
    progressBarWidth.value = MAX_LENGTH.value;

    let values = [...props.values];
    if (originalMinValue.value && originalMaxValue.value) {
      values = [originalMinValue.value || values[0], originalMaxValue.value || values[1]];
    }

    if (isValuesValid(values)) {
      values[0] = Math.max(props.min, values[0]);
      values[0] = Math.min(values[0], props.max);
      values[1] = Math.max(props.min, values[1]);
      values[1] = Math.min(values[1], props.max);

      const leftValues = calculateBlockLeft(values[0], values[1]);
      refreshProgressBar(leftValues[0], leftValues[1]);
      refreshBlock(leftValues[0], leftValues[1]);
    }
  };

  const reset = () => {
    const leftValues = calculateBlockLeft(props.min, props.max);
    refreshProgressBar(leftValues[0], leftValues[1]);
    refreshBlock(leftValues[0], leftValues[1]);
  };

  // 生命周期
  onMounted(() => {
    refresh();
  });

  // 监听属性变化
  watch(
    () => props.width,
    () => {
      refresh();
    }
  );

  watch(
    () => props.blockSize,
    () => {
      refresh();
    }
  );

  watch(
    () => props.min,
    () => {
      refresh();
    }
  );

  watch(
    () => props.max,
    () => {
      refresh();
    }
  );

  watch(
    () => props.values,
    newVal => {
      if (isValuesValid(newVal)) {
        const leftValues = calculateBlockLeft(newVal[0], newVal[1]);
        refreshProgressBar(leftValues[0], leftValues[1]);
        refreshBlock(leftValues[0], leftValues[1]);
      }
    },
    { deep: true, immediate: true }
  );

  defineExpose({
    refresh,
    reset
  });
</script>

<style lang="scss" scoped>
  .range-slider {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .range-bar {
    position: relative;
    flex: 1;
    margin: 0 12rpx;
    z-index: 1;
  }

  .range-bar-bg {
    position: relative;
    height: 100%;
    border-radius: 64rpx;
  }

  .range-bar-progress {
    position: absolute;
    top: 0;
    background-color: red;
    z-index: 1;
  }

  .block {
    position: absolute;
    box-shadow: 0px 1.2px 4.8px 0px rgba(13, 0, 158, 0.12);
    z-index: 2;
    border-radius: 76rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .block .bg {
    @include flex-center;
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 9999rpx;
    transition: all 300ms ease;
    box-shadow: 0px 1.2px 4.8px 0px rgba(13, 0, 158, 0.12);

    .dot {
      width: 12rpx;
      height: 12rpx;
      background-color: #6365e0;
      border-radius: 50%;
    }
  }
</style>
