<template>
  <scroll-view
    class="custom-scroll-view"
    scroll-y
    refresher-default-style="none"
    :refresher-enabled="enableRefresh && !showEmpty"
    :refresher-triggered="refresherTriggered"
    :refresher-threshold="refreshThreshold"
    :refresher-background="refreshBackground"
    @refresherrefresh="handleRefresh"
    @scrolltolower="handleLoadMore"
    @refresherpulling="handlePulling"
    :scroll-with-animation="true"
  >
    <template #refresher>
      <view class="refresh_indicator">
        <view v-if="!refreshing" class="flex_dc">
          <image class="pulldown" :class="moveState == 1 ? 'rot' : ''" src="/assets/images/pulldown.png" />
          <text>{{ moveState === 0 ? '下拉刷新' : '松开刷新' }}</text>
        </view>
        <view v-else class="flex_dc">
          <image class="loading" src="/assets/images/loading2.gif" />
          <text>正在加载</text>
        </view>
      </view>
    </template>

    <view class="content-wrapper" :class="{ flex_dc: showEmpty }">
      <template v-if="showEmpty">
        <slot name="empty">
          <Empty :description="emptyText" />
        </slot>
      </template>
      <template v-else>
        <slot></slot>
      </template>

      <view v-if="showBottomLoading" class="loading-more">
        <image class="loading" src="/assets/images/loading2.gif" />
        <text>加载中...</text>
      </view>
      <view v-else-if="showNoMore" class="no-more">
        <text>{{ noMoreText }}</text>
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
  import Empty from '@/oeui/new/Empty';
  import { ref, computed } from 'vue';

  const props = defineProps({
    // 列表数据
    list: {
      type: Array,
      default: () => []
    },
    // 是否正在加载（用于加载更多）
    loading: {
      type: Boolean,
      default: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      default: true
    },

    // 是否正在刷新（用于下拉刷新）
    refreshing: {
      type: Boolean,
      default: false
    },
    // 是否启用下拉刷新
    enableRefresh: {
      type: Boolean,
      default: true
    },
    // 下拉刷新阈值
    refreshThreshold: {
      type: Number,
      default: 80
    },
    // 下拉刷新背景色
    refreshBackground: {
      type: String,
      default: '#f7f7f9'
    },

    // 没有更多数据时的提示文字
    noMoreText: {
      type: String,
      default: '没有更多了'
    },
    // 空状态提示文字
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    // 是否显示底部加载状态
    showBottom: {
      type: Boolean,
      default: true
    }
  });

  const emit = defineEmits(['refresh', 'loadMore']);

  // 下拉刷新相关状态
  const refresherTriggered = ref(false);
  const moveState = ref(0);

  const showEmpty = computed(() => {
    return !props.loading && !props.refreshing && props.list.length === 0;
  });

  const showBottomLoading = computed(() => {
    return props.showBottom && props.loading && !props.refreshing;
  });

  const showNoMore = computed(() => {
    return props.showBottom && !props.hasMore && props.list.length > 0;
  });

  // 下拉刷新
  const handleRefresh = () => {
    refresherTriggered.value = true;
    moveState.value = 0;
    emit('refresh', () => {
      refresherTriggered.value = false;
    });
  };

  // 监听下拉
  const handlePulling = e => {
    const { dy } = e.detail;
    if (dy < 0) return;
    moveState.value = dy >= props.refreshThreshold ? 1 : 0;
  };

  // 处理加载更多
  const handleLoadMore = () => {
    if (props.hasMore && !props.loading && !props.refreshing) {
      emit('loadMore');
    }
  };
</script>

<style lang="scss" scoped>
  .custom-scroll-view {
    @include flex-column;
    width: 100%;
    height: 100%;

    .content-wrapper {
      min-height: 100%;
    }

    .refresh_indicator {
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #787878;
      font-size: 28rpx;

      .pulldown {
        width: 48rpx;
        height: 48rpx;
        transition: all 0.2s;
        vertical-align: middle;
        margin-right: 4rpx;

        &.rot {
          transform: rotate(180deg);
        }
      }

      .loading {
        width: 36rpx;
        height: 36rpx;
        vertical-align: middle;
        margin-right: 4rpx;
      }
    }

    .loading-more,
    .no-more {
      @include flex-center;
      padding: 24rpx 0;
      color: #666;
      font-size: 26rpx;

      .loading {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
    }
  }
</style>
