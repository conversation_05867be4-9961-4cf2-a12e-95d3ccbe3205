<template>
  <view class="goal_row">
    <view class="block"></view>
    <view class="goal_row_text">{{ label }}</view>
    <view class="goal_row_value">{{ prefix }}{{ formatter(value, false, true) }}{{ suffix }}</view>
  </view>
</template>

<script setup>
  defineProps({
    label: String,
    value: [Number, String],
    prefix: {
      type: String,
      default: ''
    },
    suffix: {
      type: String,
      default: ''
    },
    formatter: {
      type: Function,
      default: v => v
    }
  });
</script>

<style lang="scss" scoped>
  .goal_row {
    @include flex-align-center;
    margin-bottom: 40rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .block {
      background-color: #fff;
      border: 8rpx solid #6365e0;
      padding: 4rpx;
      box-sizing: border-box;
      border-radius: 6rpx;
      margin-top: 5rpx;
    }

    &_text {
      font-size: 28rpx;
      color: #666;
      margin: 0 12rpx;
    }

    &_value {
      font-size: 28rpx;
      color: #6365e0;
    }
  }
</style>
