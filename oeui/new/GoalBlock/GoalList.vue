<template>
  <view class="goal_block">
    <GoalItem v-for="item in goalItems" :key="item.label" :label="item.label" :value="item.value" :prefix="item.prefix" :suffix="item.suffix" :formatter="item.formatter" />
  </view>
</template>

<script setup>
  import { computed } from 'vue';
  import { formatNumberWithCommas } from '@/utils/hooks';
  import GoalItem from './GoalItem';

  const props = defineProps({
    financeData: {
      type: Object,
      required: true
    }
  });

  const goalItems = computed(() => [
    {
      label: '完成',
      value: props.financeData.yeji_amount,
      prefix: '¥',
      suffix: '',
      formatter: formatNumberWithCommas
    },
    {
      label: '完成率',
      value: Math.min(Math.max(props.financeData.complete, 0), 100),
      prefix: '',
      suffix: '%',
      formatter: v => v
    },
    {
      label: '目标',
      value: props.financeData.saletarget,
      prefix: '¥',
      suffix: '',
      formatter: formatNumberWithCommas
    }
  ]);
</script>

<style lang="scss" scoped>
  .goal_block {
    margin-right: 32rpx;
  }
</style>
