<template>
  <PopupContainer ref="popup" :title="title" :showFooter="!!(shouldShowArea || hasSearchResults) && !hasSearchResults" @confirm="handleConfirm" @close="close">
    <template #content>
      <view class="popup_search">
        <EasyInput v-model="searchKeyword" placeholder="快速搜索地区" />
      </view>

      <view class="area_content">
        <Tabs v-if="shouldShowArea" :tabs="tabs" :active="currentStep" @change="switchStep" />
        <scroll-view
          class="area_scroll_view"
          :class="{
            empty: !shouldShowArea && !hasSearchResults
          }"
          scroll-y
          :scroll-top="scrollTop"
          :scroll-with-animation="true"
        >
          <!-- 地区列表 -->
          <template v-if="shouldShowArea">
            <view class="area_option_list">
              <view
                v-for="item in currentAreaSource"
                :key="item.value"
                class="area_option_list_item"
                :class="{ selected: isSelected(item) }"
                @click="selectArea(item)"
                :data-unlimited="item.isUnlimited"
                :id="`option_${currentStep}_${item.value}`"
              >
                <text class="item_text">{{ item.text }}</text>
              </view>
            </view>
          </template>

          <!-- 搜索结果 -->
          <template v-else-if="hasSearchResults">
            <view class="search_result">
              <view v-for="result in searchResults" :key="result.value" class="search_result_item" @click="handleSearchResult(result)">
                <text class="item_text">{{ result.text }}</text>
                <text class="select_text">选择</text>
              </view>
            </view>
          </template>

          <Empty v-else :description="emptyDescription" />
        </scroll-view>
      </view>
    </template>
  </PopupContainer>
</template>

<script setup>
import { ref, defineExpose, reactive, computed, nextTick, watch } from 'vue';
import PopupContainer from './PopupContainer.vue';
import Tabs from './Tabs.vue';
import EasyInput from './EasyInput';
import Empty from './Empty';

import { getAreaList } from '@/api/common.js';
import { removeChildren } from '@/utils/hooks.js';
import { useAreaSearch } from '@/composables/useAreaSearch';

const AREA_TYPES = ['province', 'city', 'district'];
const UNLIMITED_OPTION = {
  value: 'all',
  text: '全部',
  isUnlimited: true,
  __isSpecial: true
};

const scrollTop = ref(0);

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      province: null,
      city: null,
      district: null
    })
  },
  title: {
    type: String,
    default: '选择地区'
  }
});

const emit = defineEmits(['confirm', 'update:modelValue']);

// 居住地(district)、户籍地(hometown)
const areas = ref([]);
const areaType = ref('district');
const currentStep = ref(0);
const popup = ref(null);
const selectedAreas = reactive({
  province: null,
  city: null,
  district: null
});

const shouldShowArea = computed(() => {
  return !searchKeyword.value && currentAreaSource.value.length;
});

// 搜索逻辑
const { searchKeyword, searchResults, hasSearchResults, emptyDescription, findAreaByValue } = useAreaSearch(areas);

// 获取地区数据源
const getAreaData = async () => {
  const area = uni.getStorageSync(areaType.value);
  if (area) {
    areas.value = JSON.parse(area);
    return;
  }

  try {
    const action = ['district', 'cond_district'].includes(areaType.value) ? 'area' : areaType.value;
    uni.showLoading({ title: '加载中' });
    const res = await getAreaList({ c: 'picker', a: action });
    if (res.ret === 1) {
      areas.value = res.result || [];
      uni.setStorageSync(areaType.value, JSON.stringify(res.result));
    }
  } catch (error) {
    areas.value = [];
    console.log(error);
  } finally {
    uni.hideLoading();
  }
};

const currentAreaSource = computed(() => {
  if (currentStep.value === 0) return areas.value;

  let source = [];
  if (currentStep.value === 1) {
    source = selectedAreas.province?.children || [];
  } else {
    source = selectedAreas.city?.children || [];
  }

  // 为市、区两级数据添加"全部"选项
  if (currentStep.value > 0 && source.length > 0) {
    const hasUnlimited = source.some(item => item.isUnlimited);
    return hasUnlimited ? source : [UNLIMITED_OPTION, ...source];
  }

  return source;
});

const tabs = computed(() => {
  const steps = [];

  steps.push({
    text: selectedAreas.province?.text || '请选择省',
    index: 0,
    enabled: true
  });

  // 如果选择了省的"全部"，不显示市
  if (selectedAreas.province && selectedAreas?.province?.children?.length && !selectedAreas.province.isUnlimited) {
    steps.push({
      text: selectedAreas.city?.text || '选择市',
      index: 1,
      enabled: true
    });

    // 如果选择了市的"全部"，不显示区
    if (selectedAreas.city && selectedAreas?.city?.children?.length && !selectedAreas.city.isUnlimited) {
      steps.push({
        text: selectedAreas.district?.text || '选择区',
        index: 2,
        enabled: true
      });
    }
  }

  return steps;
});

// 滚动到选中元素可视区域
const scrollToSelected = async () => {
  const currentType = AREA_TYPES[currentStep.value];
  const selectedItem = selectedAreas[currentType];

  await nextTick();

  // 置顶
  if (!selectedItem) {
    scrollTop.value = 1;
    return;
  }

  const query = uni.createSelectorQuery().in(popup.value);
  query.select(`#option_${currentStep.value}_${selectedItem.value}`).boundingClientRect();
  query.select('.area_option_list').boundingClientRect();
  query.exec(res => {
    if (res && res[0] && res[1]) {
      const itemRect = res[0];
      const listRect = res[1];
      const scrollDistance = itemRect.top - listRect.top - listRect.height / 2 + itemRect.height / 2;
      scrollTop.value = scrollDistance > 0 ? scrollDistance : 0;
    }
  });
};

const switchStep = async step => {
  if (step === 1 && !selectedAreas.province) return;
  if (step === 2 && !selectedAreas.city) return;

  currentStep.value = step;
  scrollToSelected();
};

const selectArea = area => {
  const currentType = AREA_TYPES[currentStep.value];

  // 先清除后续选择
  if (currentType === 'province') {
    selectedAreas.city = null;
    selectedAreas.district = null;
  } else if (currentType === 'city') {
    selectedAreas.district = null;
  }

  // 设置当前选择
  selectedAreas[currentType] = area;

  // 如果选择了"全部"，不进入下一级
  if (area.isUnlimited) return;

  // 自动进入下一级
  switch (currentType) {
    case 'province':
      if (area.children?.length) {
        currentStep.value = 1;
      }
      break;
    case 'city':
      if (area.children?.length) {
        currentStep.value = 2;
      }
      break;
  }
};

const isSelected = area => {
  const currentType = AREA_TYPES[currentStep.value];
  return selectedAreas[currentType]?.value === area.value;
};

const resetState = () => {
  currentStep.value = 0;
  Object.keys(selectedAreas).forEach(key => {
    if (!props.modelValue[key]) {
      selectedAreas[key] = null;
    }
  });
};

// 处理数据回显
const handleInitialSelected = initialSelected => {
  const { province, city, district } = initialSelected;
  if (!province) return;

  currentStep.value = 0;

  const provinceItem = areas.value.find(item => item.value === province.value);
  if (!provinceItem) return;
  selectedAreas.province = provinceItem;

  if (provinceItem.isUnlimited) {
    selectedAreas.province = UNLIMITED_OPTION;
    return;
  }

  // 处理市
  if (city && provinceItem.children) {
    currentStep.value = 1;
    const cityItem = provinceItem.children.find(item => item.value === city.value);
    if (cityItem) {
      selectedAreas.city = cityItem;

      if (cityItem.isUnlimited) {
        selectedAreas.city = UNLIMITED_OPTION;
        return;
      }

      // 处理区
      if (district && cityItem.children) {
        currentStep.value = 2;
        const districtItem = cityItem.children.find(item => item.value === district.value);
        if (districtItem) {
          selectedAreas.district = districtItem;

          if (districtItem.isUnlimited) {
            selectedAreas.district = UNLIMITED_OPTION;
          }
        }
      }
    }
  }
};

// 处理选择搜索结果
const handleSearchResult = result => {
  const originalItem = {
    ...result,
    text: result.path[result.path.length - 1]
  };

  const { province, city, district } = findAreaByValue(originalItem.value);

  if (province) {
    selectedAreas.province = province;
    currentStep.value = 0;

    if (city) {
      selectedAreas.city = city;
      currentStep.value = 1;

      if (district) {
        selectedAreas.district = district;
        currentStep.value = 2;
      }
    }
  }

  searchKeyword.value = '';
  searchResults.value = [];
};

const generateText = area => {
  const { province, city, district } = area;
  const provinceText = province?.text || '';
  const cityText = city?.text === '全部' ? '' : city?.text || '';
  const districtText = district?.text === '全部' ? '' : district?.text || '';

  switch (currentStep.value) {
    case 0:
      return provinceText;
    case 1:
      return `${provinceText} ${cityText}`.trim();
    case 2:
      return `${provinceText} ${cityText} ${districtText}`.trim();
    default:
      return '';
  }
};
const handleConfirm = () => {
  if (!selectedAreas.province) {
    uni.showToast({ title: '请选择省', icon: 'none' });
    return;
  }

  // 生成ID集合的函数
  const generateIdCombinations = () => {
    const result = [];
    const provinceId = selectedAreas.province.value;

    // 1. 只选择了省（全部市）
    if (!selectedAreas.city || selectedAreas.city.isUnlimited) {
      selectedAreas.province.children?.forEach(city => {
        if (city.children && city.children.length > 0) {
          city.children.forEach(district => {
            result.push([provinceId, city.value, district.value]);
          });
        } else {
          result.push([provinceId, city.value]);
        }
      });

      result.push([provinceId]);
      return result;
    }

    // 2. 选择了省和市（全部区）
    const cityId = selectedAreas.city.value;
    if (!selectedAreas.district || selectedAreas.district.isUnlimited) {
      if (selectedAreas.city.children) {
        selectedAreas.city.children.forEach(district => {
          result.push([provinceId, cityId, district.value]);
        });
      } else {
        result.push([provinceId, cityId]);
      }

      result.push([provinceId, cityId]);
      return result;
    }

    const districtId = selectedAreas.district.value;
    result.push([provinceId, cityId, districtId]);
    return result;
  };

  let area = {};
  switch (currentStep.value) {
    case 0:
      area = {
        province: selectedAreas.province,
        city: null,
        district: null
      };
      break;
    case 1:
      area = {
        province: selectedAreas.province,
        city: selectedAreas.city,
        district: null
      };
      break;
    case 2:
      area = {
        province: selectedAreas.province,
        city: selectedAreas.city,
        district: selectedAreas.district
      };
      break;
  }

  const text = generateText(area);
  const ids = generateIdCombinations();
  emit('confirm', { text, ids, area: removeChildren(area) });
  close();
};

const open = async type => {
  popup.value?.open('bottom');
  areaType.value = type;

  // 获取地区数据源
  await getAreaData();

  // 重置状态
  resetState();

  // 数据回显
  const initialSelected = props.modelValue;
  if (initialSelected.province) {
    handleInitialSelected(initialSelected);

    // 确保选择全部后显示正确的层级
    if (initialSelected.city?.isUnlimited) {
      currentStep.value = 1;
    } else if (initialSelected.district?.isUnlimited) {
      currentStep.value = 2;
    }
  }
};

const close = () => {
  popup.value?.close();
};

watch(
  () => currentStep.value,
  index => {
    scrollToSelected();

    if (index === 1 && !selectedAreas.city) {
      selectedAreas.city = JSON.parse(JSON.stringify(UNLIMITED_OPTION));
    }
  }
);

defineExpose({
  open,
  close
});
</script>

<style lang="scss">
.area_content {
  margin-top: 0.2667rem;
}

.area_scroll_view ::-webkit-scrollbar {
  display: none;
}

.area_scroll_view.empty {
  @include flex-center;
  height: 100%;
}

.area_scroll_view {
  min-height: 660rpx;
  max-height: 660rpx;

  .item_text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .area_option_list {
    @include grid(3, 24rpx);
    margin-top: 0.2133rem;
    align-content: start;

    &_item {
      width: 100%;
      height: 64rpx;
      background-color: #fff;
      color: #000;
      display: block;
      line-height: 64rpx;
      text-align: center;
      font-size: 28rpx;
      cursor: pointer;
      border: 1rpx solid #f7f7f9;
      border-radius: 16rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      margin-bottom: 24rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:active {
        background: #f7f7f9;
      }

      &.selected {
        background: #6365e0;
        border: none;
        color: #fff;
      }
    }
  }

  .search_result {
    &_item {
      @include flex-align-center;
      @include flex-between;
      width: 100%;
      height: 82rpx;
      background-color: #fff;
      font-size: 32rpx;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .item_text {
        flex: 1;
      }

      .select_text {
        width: 100rpx;
        color: #2c5ce1;
        text-align: right;
      }
    }
  }
}
</style>
