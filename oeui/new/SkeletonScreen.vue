<template>
  <view class="skeleton-container">
    <!-- 标题骨架 -->
    <view class="skeleton-title" v-if="showTitle"></view>

    <!-- 菜单骨架 -->
    <view class="skeleton-menu-row" v-if="showMenu">
      <view class="skeleton-menu-item" v-for="i in menuCount" :key="'menu-' + i"></view>
    </view>

    <!-- 筛选栏骨架 -->
    <view class="skeleton-filter" v-if="showFilter"></view>

    <!-- 数据图表骨架 -->
    <view class="skeleton-chart-row" v-if="showChart">
      <view class="skeleton-chart"></view>
      <view class="skeleton-goals">
        <view class="skeleton-goal" v-for="i in 3" :key="'goal-' + i"></view>
      </view>
    </view>

    <!-- 统计数据骨架 -->
    <view class="skeleton-bold"></view>
    <view class="skeleton-stats" v-if="showStats">
      <view class="skeleton-stat-item" v-for="i in statCount" :key="'stat-' + i"></view>
    </view>
  </view>
</template>

<script setup>
  const props = defineProps({
    showTitle: { type: Boolean, default: true },
    showMenu: { type: Boolean, default: true },
    showFilter: { type: Boolean, default: true },
    showChart: { type: Boolean, default: true },
    showStats: { type: Boolean, default: true },
    showFunnel: { type: Boolean, default: true },
    menuCount: { type: Number, default: 8 },
    statCount: { type: Number, default: 6 }
  });
</script>

<style lang="scss" scoped>
  .skeleton-container {
    height: calc(100vh - 64rpx);
    padding: 32rpx;
    background-color: #fff;
  }

  // 优化后的骨架动画
  @keyframes shimmer {
    0% {
      opacity: 0.6;
      transform: translateX(-100%) skewX(-15deg);
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
      transform: translateX(100%) skewX(-15deg);
    }
  }

  .skeleton-base {
    position: relative;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 8rpx;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
      animation: shimmer 1.5s infinite ease-in-out;
      transform: translateX(-100%);
    }
  }

  .skeleton-title {
    @extend .skeleton-base;
    width: 100%;
    height: 80rpx;
    margin-bottom: 30rpx;
    border-radius: 12rpx;
  }

  .skeleton-menu-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;
    margin: 16rpx 0;
  }

  .skeleton-menu-item {
    @extend .skeleton-base;
    height: 120rpx;
    border-radius: 16rpx;
  }

  .skeleton-filter {
    @extend .skeleton-base;
    height: 80rpx;
    margin: 64rpx 0;
    border-radius: 12rpx;
  }

  .skeleton-chart-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 64rpx;
    align-items: center;
  }

  .skeleton-chart {
    @extend .skeleton-base;
    width: 240rpx;
    height: 240rpx;
    border-radius: 50%;
  }

  .skeleton-goals {
    flex: 1;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
  }

  .skeleton-goal {
    @extend .skeleton-base;
    height: 40rpx;
    margin-bottom: 40rpx;
    width: 80%;
    border-radius: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .skeleton-bold {
    width: 50%;
    height: 60rpx;
    @extend .skeleton-base;
    margin-bottom: 32rpx;
  }

  .skeleton-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx 100rpx;
    padding: 0 0 64rpx 0;
  }

  .skeleton-stat-item {
    @extend .skeleton-base;
    height: 80rpx;
    border-radius: 12rpx;
  }

  .skeleton-funnel {
    @extend .skeleton-base;
    height: 300rpx;
    margin-top: 64rpx;
    border-radius: 12rpx;
  }
</style>
