import { createApp, nextTick } from 'vue'
import GlobalModal from './GlobalModal.vue'

let instance = null
let mountNode = null

const createModal = (options) => {
  // 销毁旧实例
  if (instance) {
    document.body.removeChild(mountNode)
    instance = null
    mountNode = null
  }

  // 创建新实例
  mountNode = document.createElement('div')
  document.body.appendChild(mountNode)

  const app = createApp(GlobalModal, {
    title: options.title,
    content: options.content,
    confirmText: options.confirmText,
    cancelText: options.cancelText,
    isColumn: options.isColumn,
    onConfirm: options.onConfirm,
    onCancel: options.onCancel
  })

  instance = app.mount(mountNode)
  return instance
}

export const showModal = async (options) => {
  const modal = createModal({
    ...options,
    onConfirm: () => {
      options.success?.({ confirm: true, cancel: false })
      destroyModal()
    },
    onCancel: () => {
      options.success?.({ confirm: false, cancel: true })
      destroyModal()
    }
  })

  await nextTick()
  modal.show()
  return modal
}

const destroyModal = () => {
  if (instance && mountNode) {
    instance.hide()
    setTimeout(() => {
      document.body.removeChild(mountNode)
      mountNode = null
      instance = null
    }, 300)
  }
}