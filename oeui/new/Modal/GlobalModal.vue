<!-- GlobalModal.vue -->
<template>
  <uni-popup ref="popup" type="center" class="modal-popup" :is-mask-click="false" :safe-area="false" @mask-click="handleCancel">
    <view class="modal">
      <text class="title">{{ title }}</text>
      <text class="content">{{ content }}</text>
      <view class="btns">
        <view v-if="!isColumn" class="small-row">
          <view class="small-btn cancel-small-btn" @click="handleCancel">{{ cancelText }}</view>
          <view class="small-btn confirm-small-btn" @click="handleConfirm">{{ confirmText }}</view>
        </view>
        <view v-else class="small-row column-row">
          <view class="small-btn single-confirm-btn" @click="handleConfirm">{{ confirmText }}</view>
          <view v-if="cancelText" class="single-cancel-btn" @click="handleCancel">{{ cancelText }}</view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
  import { ref } from 'vue';
  import { debounce } from '@/utils/hooks.js';

  const props = defineProps({
    title: String,
    content: String,
    confirmText: { type: String, default: '确定' },
    cancelText: { type: String, default: '取消' },
    isColumn: Boolean,
    onConfirm: Function,
    onCancel: Function
  });

  const popup = ref(null);
  const show = () => popup.value?.open();
  const hide = () => popup.value?.close();

  const handleConfirm = () => {
    debouncedConfirm();
  };

  const debouncedConfirm = debounce(() => {
    props.onConfirm();
  }, 300);

  const handleCancel = () => {
    props.onCancel();
    hide();
  };

  defineExpose({ show, hide });
</script>
<style lang="scss" scoped>
  .modal-popup {
    z-index: 9999;
    ::v-deep .uni-popup__wrapper.center {
      width: 80%;
    }

    .modal {
      @include flex-column;
      @include flex-center;
      background: #fff;
      border-radius: 32rpx;
      padding: 48rpx 48rpx 32rpx 48rpx;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #000;
        line-height: 1;
        margin-bottom: 32rpx;
      }

      .content {
        font-size: 28rpx;
        color: #000;
        margin-bottom: 48rpx;
        text-align: center;
        line-height: 44rpx;
      }

      .btns {
        width: 100%;
        .small-row {
          @include flex-center;
          justify-content: space-between;
        }

        .column-row {
          @include flex-column;
        }

        .small-btn {
          @include flex-center;
          height: 80rpx;
          background: #6365e0;
          color: #fff;
          font-size: 32rpx;
          border-radius: 99px;

          &:active {
            background: #8284e6;
          }
        }

        .confirm-small-btn,
        .cancel-small-btn {
          width: 220rpx;
        }

        .single-confirm-btn {
          width: 410rpx;
        }

        .single-cancel-btn {
          font-size: 28rpx;
          color: #000;
          line-height: 1;
          margin-top: 32rpx;
        }

        .cancel-small-btn {
          background: #f7f7f9;
          color: #000;

          &:active {
            color: #fff;
          }
        }
      }
    }
  }
</style>
