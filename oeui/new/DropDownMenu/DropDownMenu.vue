<template>
  <view class="dropdown-menu-container" :id="id">
    <slot></slot>
  </view>
</template>

<script setup>
import { toRefs, provide, ref } from 'vue';
const props = defineProps({
  backgroundColor: {
    type: String,
    default: '#ffffff'
  },
  height: {
    type: String,
    default: '90rpx'
  },
  justifyContent: {
    type: String,
    default: 'space-around'
  },
  titleColor: {
    type: String,
    default: '#000000'
  },
  activeColor: {
    // 菜单标题和选项的选中态颜色
    type: String,
    default: '#6365E0'
  },
  zIndex: {
    // 菜单栏 z-index 层级
    type: [String, Number],
    default: 100
  },
  id: {
    type: String,
    default: 'dropdown-menu-container-root'
  }
});
const root = ref();
const rootTop = ref(0);

provide('activeColor', props.activeColor);
provide('titleColor', props.titleColor);
provide('height', props.height);
provide('zIndex', props.zIndex);
provide('rootTop', rootTop);
const { backgroundColor, height, justifyContent } = toRefs(props);
const close = () => {
  uni.$emit('toggle', '');
};
uni.$on('getRootTop', () => {
  uni
    .createSelectorQuery()
    .select(`#${props.id}`)
    .boundingClientRect(data => {
      const { bottom } = data;
      // 44为搜索框
      //  + 44
      rootTop.value = bottom + 'px';
    })
    .exec();
});
defineExpose({
  close
});

const menuEmit = defineEmits(['confirm']);
provide('menuEmit', menuEmit);
</script>

<style scoped lang="scss">
.dropdown-menu-container {
  width: 100%;
  background-color: v-bind(backgroundColor);
  height: v-bind(height);
  display: flex;
  justify-content: v-bind(justifyContent);
  align-items: center;
  position: relative;
  z-index: v-bind(zIndex);
}
</style>
