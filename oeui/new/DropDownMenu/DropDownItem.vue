<template>
  <view class="dropdown-item-container" @touchmove.stop.prevent>
    <view class="dropdown-item-title" :class="{ 'expand-open': showExpand, selected: isSelected }" @click="handleToggle">
      {{ title }}
      <text class="iconfont icon-zhankai-011" :class="{ 'rotate-180': showExpand, 'rotate-360': !showExpand }"></text>
    </view>
    <view class="transparent-overlay" v-show="showExpand" @touchmove.stop.prevent @click="handleToggle"></view>
    <Transition name="fade">
      <view class="black-overlay" @touchmove.stop.prevent @click="handleToggle" v-show="showExpand">
        <Transiton name="slide">
          <view class="options-container" v-show="showExpand">
            <view class="options-list" @click.stop>
              <scroll-view class="scroll-view" :scroll-y="true">
                <slot :doSelect="handleSelect">
                  <view class="option-item" :class="{ active: item.value === selectItem?.value }" v-for="(item, index) in optionList" :key="index" @click="handleSelect(item)">
                    <view class="option-label">{{ item.text }}</view>
                    <text class="iconfont icon-xuanzhong" v-if="item.value === selectItem?.value"></text>
                  </view>
                </slot>
              </scroll-view>
            </view>
            <!-- <view class="dropdown-btn-container" @click.stop>
							<view class="dropdown-btn reset-btn" @click="handleReset">重置</view>
							<view class="dropdown-btn confirm-btn" @click="handleConfirm">确定</view>
						</view> -->
          </view>
        </Transiton>
      </view>
    </Transition>
  </view>
</template>

<script setup>
import { computed, ref, inject, watch, onMounted, provide, reactive } from 'vue';
const props = defineProps({
  modelValue: {
    // 当前选中项对应的 value
    type: [String, Number],
    default: ''
  },
  multiple: {
    // 是否多选
    type: Boolean,
    default: false
  },
  title: String, // 菜单项标题,不传则取当前选中项text
  options: [Array, Function] // 获取选择数据函数 返回一个  [{ text: '',value: '',icon: ''}] 格式的数组
});
const emit = defineEmits(['update:modelValue', 'change', 'clear']);
const activeColor = inject('activeColor');
const titleColor = inject('titleColor');
const height = inject('height');
const zIndex = inject('zIndex');
const rootTop = inject('rootTop');
const menuEmit = inject('menuEmit');
const showExpand = ref(false);
const selectItem = ref();
const title = computed(() => {
  return props.title || selectItem.value.text;
});
const optionList = ref([]);
const statusList = ref([]);
onMounted(() => {
  loadOptionData();
});

const loadOptionData = () => {
  if (!props.options) {
    return;
  }
  if (Array.isArray(props.options)) {
    optionList.value = props.options;
    selectItem.value = optionList.value.find(item => item.value === props.modelValue);
  } else {
    const result = props.options();
    if (result.__proto__.constructor.name === 'Promise') {
      result.then(data => {
        optionList.value = data;
      });
    } else {
      optionList.value = result;
    }
  }
};

watch(showExpand, val => {
  if (val) uni.$emit('getRootTop');
});
uni.$on('toggle', value => {
  if (!value || value !== props.title) {
    showExpand.value = false;
  }
});
const arrowIconColor = computed(() => {
  return titleColor === '#fff' ? '#fff' : '#dcdee0';
});
const toggle = val => {
  uni.$emit('toggle', title.value);
  showExpand.value = val || !showExpand.value;
};
const handleToggle = () => {
  toggle();
};
const handleSelect = item => {
  if (props.multiple) {
    if (statusList.value.includes(item.value)) {
      statusList.value.splice(statusList.value.indexOf(item.value), 1);
      return;
    }
    statusList.value.push(item.value);
  } else {
    selectItem.value = item;
  }
  emit('clear');
  handleConfirm();
};

const handleConfirm = () => {
  if (props.multiple) {
    emit('update:modelValue', statusList.value);
    emit('change', statusList.value);
    menuEmit('confirm');
    toggle(false);
    return;
  }
  emit('update:modelValue', selectItem.value?.value);
  emit('change', selectItem.value);
  menuEmit('confirm');
  toggle(false);
};

const handleReset = () => {
  if (props.multiple) {
    statusList.value = [];
    emit('clear');
    emit('update:modelValue', null);
    menuEmit('confirm');
    toggle(false);
    return;
  }
  emit('clear');
  emit('update:modelValue', null);
  menuEmit('confirm');

  selectItem.value = null;
  toggle(false);
};

/**
 * 是否选中
 */
const isSelected = computed(() => {
  if (props.multiple && statusList.value.length > 0) {
    return true;
  }
  return (selectItem.value && selectItem.value.value) || false;
});

provide(
  'DownItemProvide',
  reactive({
    handleSelect
  })
);
</script>

<style scoped lang="scss">
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.slide-enter-from,
.slide-leave-to {
  top: -500px !important;
}
.slide-enter-active,
.slide-leave-active {
  transition: top 0.2s ease;
}
.slide-enter-to,
.slide-leave-from {
  top: 0 !important;
}
.rotate-180 {
  transition: transform 0.2s ease;
  transform: rotate(180deg);
}
.rotate-360 {
  transition: transform 0.2s ease;
  transform: rotate(360deg);
}
.dropdown-item-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .selected {
    color: v-bind(activeColor) !important;
  }
  .dropdown-item-title {
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
    color: v-bind(titleColor);
    z-index: calc(v-bind(zIndex) + 2);
    padding: 0 26rpx;
    font-size: 32rpx;
    // &::after {
    // 	position: absolute;
    // 	top: 50%;
    // 	right: 0;
    // 	border: 6rpx solid;
    // 	border-color: transparent transparent v-bind(titleColor) v-bind(titleColor);
    // 	transform: translateY(calc(-50% - 2rpx)) rotate(-45deg);
    // 	opacity: .8;
    // 	content: '';
    // 	// background: v-bind(titleColor);
    // }
    &.expand-open {
      color: v-bind(activeColor);
      &::after {
        border-color: transparent transparent v-bind(activeColor) v-bind(activeColor);
        transform: rotate(135deg);
      }
    }
  }
  .transparent-overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: v-bind(zIndex);
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }
  .black-overlay {
    position: fixed;
    top: v-bind(rootTop);
    bottom: 0;
    left: 0;
    right: 0;
    z-index: v-bind(zIndex);
    background: rgba(0, 0, 0, 0.7);
    overflow: hidden;
  }

  .options-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: v-bind(zIndex);
    border-top: 2rpx solid #f7f7f7;
    max-height: calc(65vh + 88rpx);
    display: flex;
    flex-direction: column;
    .options-list {
      flex: 1;
      height: 0;
      background: #fff;
      display: flex;
      .scroll-view {
        flex: 1;
      }
    }

    .option-item {
      padding: 20rpx 64rpx 20rpx 48rpx;
      display: flex;
      color: v-bind(titleColor);
      justify-content: space-between;
      align-items: center;
      line-height: 48rpx;
      position: relative;
      font-size: 32rpx;
      &.active {
        color: v-bind(activeColor);
      }
    }
  }
}
.dropdown-btn-container {
  display: flex;
  align-items: center;
  height: 88rpx;
  .dropdown-btn {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &.reset-btn {
      background: #fff;
      color: #000000;
      box-shadow: 0rpx -6rpx 60rpx 0rpx rgba(109, 114, 120, 0.1);
      border-radius: 0 0 0 20rpx;
    }
    &.confirm-btn {
      background: #3f78f6;
      color: #fff;
      box-shadow: 0rpx -6rpx 60rpx 0rpx rgba(109, 114, 120, 0.1);
      border-radius: 0 0 20rpx 0;
    }
  }
}
</style>
