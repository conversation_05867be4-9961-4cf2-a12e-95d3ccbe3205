<template>
  <uni-popup ref="popupRef" :is-mask-click="maskClick" :safe-area="false" type="bottom">
    <view class="popup-container" :class="{ 'default-pad': isDefaultPad }">
      <view class="slider">
        <view class="line"></view>
      </view>

      <view v-if="titlePosition=='left'" class="popup-header" :class="{ 'default-pad': !isDefaultPad }">
        <text class="title">{{ title }}</text>
        <view>
          <slot name="operate"></slot>
          <text class="close-btn" @click="handleClose">取消</text>
        </view>
      </view>
      <view v-else-if="titlePosition=='center'" class="popup-header-center">
        <text class="font-info fz16">{{ title }}</text>
      </view>
      <view class="popup-content">
        <slot name="content"></slot>
      </view>

      <view v-if="!hideFooter" class="popup-footer" :class="{ showFooter: !showFooter }">
        <view class="small-row" v-if="titlePosition=='center'">
          <view class="small-btn close-small-btn" @click="handleClose">{{ closeText }}</view>
          <view class="small-btn confirm-small-btn" @click="debouncedHandleConfirm">{{ confirmText }}</view>
        </view>
        <view class="small-row" v-else-if="confirmText && showClose">
          <view class="small-btn close-small-btn" @click="handleReset">{{ closeText }}</view>
          <view class="small-btn confirm-small-btn" @click="debouncedHandleConfirm">{{ confirmText }}</view>
        </view>
        <view class="large-row" v-else>
          <view class="confirm-btn" @click="debouncedHandleConfirm">{{ confirmText }}</view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
  import { ref } from 'vue';
  import { debounce } from '@/utils/hooks.js';

  const props = defineProps({
    title: {
      type: String,
      default: '标题'
    },
    titlePosition:{
      type: String,
      default: 'left'
    },
    maskClick: {
      type: Boolean,
      default: false
    },

    // 底部栏配置
    showFooter: {
      type: [Boolean, Number],
      default: true
    },
    hideFooter: {
      type: [Boolean, Number],
      default: false
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    closeText: {
      type: String,
      default: '取消'
    },
    showClose: {
      type: Boolean,
      default: false
    },

    // 样式配置
    isDefaultPad: {
      type: Boolean,
      default: true
    }
  });

  const emit = defineEmits(['confirm', 'close', 'reset']);

  const popupRef = ref(null);

  const open = () => {
    popupRef.value?.open();
  };

  const close = () => {
    popupRef.value?.close();
  };

  const handleConfirm = () => {
    emit('confirm');
  };

  const handleReset = () => {
    emit('reset');
  };

  const debouncedHandleConfirm = debounce(handleConfirm, 200);

  const handleClose = () => {
    emit('close');
    close();
  };

  defineExpose({ open, close });
</script>

<style lang="scss" scoped>
  .default-pad {
    padding: 0 32rpx;
  }

  .popup-container {
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    box-sizing: border-box;
    padding-bottom: calc(-40rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(-40rpx + env(safe-area-inset-bottom));

    .slider {
      @include flex-center;
      padding: 16rpx 0 8rpx 0;
      margin-bottom: 24rpx;

      .line {
        width: 48rpx;
        height: 8rpx;
        background: #dcdfe6;
        border-radius: 18rpx;
      }
    }

    .popup-header,.popup-header-center {
      @include flex-between;
      margin-bottom: 32rpx;

      .title {
        color: #000;
        font-size: 36rpx;
        font-weight: 550;
      }

      .close-btn {
        color: #8898b6;
        font-size: 32rpx;
      }
    }
    .popup-header-center{
      @include flex-center
    }
    .popup-footer.showFooter {
      opacity: 0;
    }

    .popup-footer {
      @include flex-center;
      padding: 32rpx 0;
      opacity: 1;

      .small-row {
        @include flex-center;
        justify-content: space-between;
      }

      .small-btn {
        @include flex-center;
        width: 320rpx;
        height: 80rpx;
        background: #6365e0;
        color: #fff;
        font-size: 32rpx;
        border-radius: 40rpx;

        &:active {
          background: #8284e6;
        }
      }

      .close-small-btn {
        margin-right: 32rpx;
        background: #f7f7f9;
        color: #000;

        &:active {
          color: #fff;
        }
      }

      .confirm-btn {
        @include flex-center;
        width: 620rpx;
        height: 80rpx;
        background: #6365e0;
        color: #fff;
        font-size: 32rpx;
        border-radius: 40rpx;

        &:active {
          background: #8284e6;
        }
      }
    }
  }
</style>
