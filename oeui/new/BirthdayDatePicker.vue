<template>
  <PopupContainer ref="popup" :title="title" :showFooter="!!shouldShowDate" @confirm="handleConfirm" @close="close">
    <template #content>
      <view class="date_content">
        <Tabs v-if="shouldShowDate" :tabs="tabs" :active="currentStep" @change="switchStep" />
        <scroll-view
          class="date_scroll_view"
          :class="{
            empty: !shouldShowDate
          }"
          scroll-y
          :scroll-top="scrollTop"
          :scroll-with-animation="true"
        >
          <!-- 日期列表 -->
          <template v-if="shouldShowDate">
            <view class="date_option_list">
              <view
                v-for="item in currentDateSource"
                :key="item.value"
                class="date_option_list_item"
                :class="{ selected: isSelected(item) }"
                @click="selectDate(item)"
                :id="`option_${currentStep}_${item.value}`"
              >
                <text class="item_text">{{ item.text }}</text>
              </view>
            </view>
          </template>

          <Empty v-else :description="emptyDescription" />
        </scroll-view>
      </view>
    </template>
  </PopupContainer>
</template>

<script setup>
import { ref, defineExpose, reactive, computed, nextTick, watch } from 'vue';
import PopupContainer from './PopupContainer.vue';
import Tabs from './Tabs.vue';
import Empty from './Empty';

const DATE_TYPES = ['year', 'month', 'day'];

const scrollTop = ref(0);

const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: () => ({
      year: null,
      month: null,
      day: null
    })
  },
  title: {
    type: String,
    default: '选择生日'
  },
  start: {
    type: Number,
    default: 1970
  },
  end: {
    type: Number,
    default: new Date().getFullYear()
  }
});

const emit = defineEmits(['confirm', 'update:modelValue']);

const dateList = ref([]);
const currentStep = ref(0);
const popup = ref(null);
const selectedDates = reactive({
  year: null,
  month: null,
  day: null
});

const shouldShowDate = computed(() => {
  return currentDateSource.value.length;
});

const isLeapYear = year => {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
};

const getDayNum = (year, month) => {
  switch (month) {
    case 1:
    case 3:
    case 5:
    case 7:
    case 8:
    case 10:
    case 12:
      return 31;
    case 4:
    case 6:
    case 9:
    case 11:
      return 30;
    case 2:
      return isLeapYear(year) ? 29 : 28;
    default:
      return 0;
  }
};

const initDate = (start, end) => {
  dateList.value = [];
  let ind1 = 0;
  for (let i = start; i <= end; i++) {
    ind1++;
    let year = { i: ind1, value: i, text: i + '年', children: [] };
    let ind2 = 0;
    for (let j = 1; j <= 12; j++) {
      ind2++;
      let month = { i: ind2, value: j, text: j + '月', children: [] };
      let ind3 = 0;
      const days = getDayNum(i, j);
      for (let l = 1; l <= days; l++) {
        ind3++;
        let day = { i: ind3, value: l, text: l + '日', children: [] };
        month.children.push(day);
      }
      year.children.push(month);
    }
    dateList.value.push(year);
  }
};

const currentDateSource = computed(() => {
  if (currentStep.value === 0) return dateList.value;
  if (currentStep.value === 1) return selectedDates.year?.children || [];
  return selectedDates.month?.children || [];
});

const tabs = computed(() => {
  const steps = [];

  steps.push({
    text: selectedDates.year?.text || '请选择年',
    index: 0,
    enabled: true
  });

  if (selectedDates.year) {
    steps.push({
      text: selectedDates.month?.text || '选择月',
      index: 1,
      enabled: true
    });

    if (selectedDates.month) {
      steps.push({
        text: selectedDates.day?.text || '选择日',
        index: 2,
        enabled: true
      });
    }
  }

  return steps;
});

// 滚动到选中元素可视区域
const scrollToSelected = async () => {
  const currentType = DATE_TYPES[currentStep.value];
  const selectedItem = selectedDates[currentType];

  await nextTick();

  // 重置滚动位置
  if (!selectedItem) {
    scrollTop.value = 1;
    return;
  }

  const query = uni.createSelectorQuery().in(popup.value);
  query.select(`#option_${currentStep.value}_${selectedItem.value}`).boundingClientRect();
  query.select('.date_scroll_view').boundingClientRect();
  query.exec(res => {
    if (res && res[0] && res[1]) {
      const itemRect = res[0];
      const scrollViewRect = res[1];

      // 计算元素在滚动视图中的相对位置
      const itemTop = itemRect.top - scrollViewRect.top;
      const itemHeight = itemRect.height;
      const scrollViewHeight = scrollViewRect.height;

      // 计算滚动位置，使选项居中显示
      const scrollDistance = itemTop - scrollViewHeight / 2 + itemHeight / 2;

      // 确保滚动位置不小于0
      scrollTop.value = Math.max(0, scrollDistance);
    }
  });
};

const switchStep = async step => {
  if (step === 1 && !selectedDates.year) return;
  if (step === 2 && !selectedDates.month) return;

  currentStep.value = step;
  scrollToSelected();
};

const selectDate = date => {
  const currentType = DATE_TYPES[currentStep.value];

  // 先清除后续选择
  if (currentType === 'year') {
    selectedDates.month = null;
    selectedDates.day = null;
  } else if (currentType === 'month') {
    selectedDates.day = null;
  }

  // 设置当前选择
  selectedDates[currentType] = date;

  // 自动进入下一级
  switch (currentType) {
    case 'year':
      if (date.children?.length) {
        currentStep.value = 1;
      }
      break;
    case 'month':
      if (date.children?.length) {
        currentStep.value = 2;
      }
      break;
  }
};

const isSelected = date => {
  const currentType = DATE_TYPES[currentStep.value];
  return selectedDates[currentType]?.value === date.value;
};

const resetState = () => {
  currentStep.value = 0;
  Object.keys(selectedDates).forEach(key => {
    if (!props.modelValue[key]) {
      selectedDates[key] = null;
    }
  });
};

// 处理数据回显
const handleInitialSelected = initialSelected => {
  // 处理字符串格式的日期 (YYYY-MM-DD)
  if (typeof initialSelected === 'string' && initialSelected.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const [yearStr, monthStr, dayStr] = initialSelected.split('-');
    const yearValue = parseInt(yearStr);
    const monthValue = parseInt(monthStr);
    const dayValue = parseInt(dayStr);

    const yearItem = dateList.value.find(item => item.value === yearValue);
    if (!yearItem) return;
    selectedDates.year = yearItem;

    if (yearItem.children) {
      const monthItem = yearItem.children.find(item => item.value === monthValue);
      if (monthItem) {
        selectedDates.month = monthItem;

        if (monthItem.children) {
          const dayItem = monthItem.children.find(item => item.value === dayValue);
          if (dayItem) {
            selectedDates.day = dayItem;
            currentStep.value = 2;
          }
        }
      }
    }
    return;
  }
};

const generateText = date => {
  const { year, month, day } = date;
  if (!year || !month || !day) return '';

  const yearValue = year;
  const monthValue = month;
  const dayValue = day;

  return `${yearValue}-${monthValue}-${dayValue}`;
};

const handleConfirm = () => {
  if (!selectedDates.year) {
    uni.showToast({ title: '请选择年份', icon: 'none' });
    return;
  }

  if (!selectedDates.month) {
    uni.showToast({ title: '请选择月份', icon: 'none' });
    return;
  }

  if (!selectedDates.day) {
    uni.showToast({ title: '请选择日期', icon: 'none' });
    return;
  }

  const date = {
    year: String(selectedDates.year.value),
    month: String(selectedDates.month.value).padStart(2, '0'),
    day: String(selectedDates.day.value).padStart(2, '0')
  };

  const text = generateText(date);
  emit('confirm', { text, date });
  emit('update:modelValue', text);
  close();
};

const open = async () => {
  popup.value?.open('bottom');

  // 初始化日期数据
  initDate(props.start, props.end);

  // 重置状态
  resetState();

  // 数据回显
  const initialSelected = props.modelValue;
  if (initialSelected) {
    handleInitialSelected(initialSelected);
    if (selectedDates.year && selectedDates.month && selectedDates.day) {
      await nextTick();
      scrollToSelected();
    }
  }
};

const close = () => {
  popup.value?.close();
};

watch(
  () => currentStep.value,
  index => {
    scrollToSelected();
  }
);

defineExpose({
  open,
  close
});
</script>

<style lang="scss">
.date_content {
  margin-top: 0.2667rem;
}

.date_scroll_view ::-webkit-scrollbar {
  display: none;
}

.date_scroll_view.empty {
  @include flex-center;
  height: 100%;
}

.date_scroll_view {
  min-height: 660rpx;
  max-height: 660rpx;

  .item_text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .date_option_list {
    @include grid(3, 24rpx);
    margin-top: 0.2133rem;
    align-content: start;

    &_item {
      width: 100%;
      height: 64rpx;
      background-color: #fff;
      color: #000;
      display: block;
      line-height: 64rpx;
      text-align: center;
      font-size: 28rpx;
      cursor: pointer;
      border: 1rpx solid #f7f7f9;
      border-radius: 16rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      margin-bottom: 24rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:active {
        background: #f7f7f9;
      }

      &.selected {
        background: #6365e0;
        border: none;
        color: #fff;
      }
    }
  }

  .search_result {
    &_item {
      @include flex-align-center;
      @include flex-between;
      width: 100%;
      height: 82rpx;
      background-color: #fff;
      font-size: 32rpx;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .item_text {
        flex: 1;
      }

      .select_text {
        width: 100rpx;
        color: #2c5ce1;
        text-align: right;
      }
    }
  }
}
</style>
