<template>
  <view class="empty-container">
    <view class="icon-container">
      <slot name="icon">
        <image :src="image" class="empty-image" />
      </slot>
    </view>

    <text class="description">{{ description }}</text>
  </view>
</template>

<script setup>
  import { defineProps } from 'vue';
  import empty from '@/assets/svg/empty.svg';

  const props = defineProps({
    image: {
      type: String,
      default: empty
    },
    description: {
      type: String,
      default: '暂无内容'
    }
  });
</script>

<style lang="scss" scoped>
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .description {
    color: #000;
    font-size: 28rpx;
  }

  .empty-image {
    margin-bottom: 48rpx;
    width: 320rpx;
    height: 246rpx;
  }
</style>
