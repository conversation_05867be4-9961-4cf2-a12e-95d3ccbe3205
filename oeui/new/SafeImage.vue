<template>
  <image :src="effectiveSrc" @error="handleError"  />
</template>

<script setup>
  import { ref, computed } from 'vue';

  import gender1 from '@/assets/images/gender_1.png';
  import gender2 from '@/assets/images/gender_2.png';
  import genderNo from '@/assets/images/gender_no.png';

  const props = defineProps({
    src: String,
    gender: {
      type: [String, Number],
      default: null
    }
  });

  const hasError = ref(false);

  const effectiveSrc = computed(() => {
    if (hasError.value || !props.src) {
      return getDefaultAvatar(props.gender);
    }
    return props.src;
  });

  const handleError = () => {
    hasError.value = true;
  };

  const getDefaultAvatar = gender => {
    switch (gender) {
      case '1':
        return gender1;
      case '2':
        return gender2;
      default:
        return genderNo;
    }
  };
</script>
