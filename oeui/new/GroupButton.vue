<template>
  <view class="group-button-container">
    <view class="group-button-row">
      <view v-for="(item, index) in options" :key="index" @click="handleClick(item.value)" :class="['group-button', getPositionClass(index), { active: isActive(item.value) }]">
        <text v-if="item.icon" :class="item.icon.text" :style="{ color: isActive(item.value) ? '#fff' : item.icon.color }"></text>
        {{ item.text }}
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  options: {
    type: Array,
    required: true,
    validator: value => {
      return value.every(item => item.text && item.value !== undefined);
    }
  },
  modelValue: {
    type: [String, Number, Boolean],
    default: ''
  },

  isGap: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const isActive = value => {
  return props.modelValue === value;
};

const handleClick = value => {
  emit('update:modelValue', value);
  emit('change', value);
};

const getPositionClass = index => {
  if (index === 0) return props.isGap ? 'left gap' : 'left';
  if (index === props.options.length - 1) return 'right';
  return 'middle';
};
</script>

<style lang="scss" scoped>
.group-button-row {
  @include flex-align-center;

  .group-button.gap {
    margin-right: 1rpx;
  }

  .group-button {
    @include flex-center;
    width: 108rpx;
    height: 64rpx;
    background-color: #f7f7f9;
    font-size: 28rpx;
    color: #000;
    transition: all 0.2s;
    box-sizing: border-box;

    &.active {
      background-color: #6365e0;
      color: #fff;
    }

    .iconfont {
      margin-right: 4rpx;
    }

    &.left {
      border-radius: 16rpx 0 0 16rpx;
    }

    &.middle {
      margin: 0 2rpx;
    }

    &.right {
      border-radius: 0 16rpx 16rpx 0;
    }
  }
}
</style>
