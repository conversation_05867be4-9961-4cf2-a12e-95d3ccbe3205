<template>
  <view class="statistics_row" v-if="showComponent">
    <view class="title_bold">{{ title }}</view>
    <view class="statistics_list">
      <view v-for="item in visibleItems" :key="item.key" class="statistics_list_item">
        <text class="title">{{ item.title }}</text>
        <view class="value">
          <text class="num">
            {{ formatNumberWithCommas(data[item.key], false) }}
          </text>
          <text class="unit">{{ item.unit }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { computed } from 'vue';
  import { formatNumberWithCommas } from '@/utils/hooks';

  const STATS_CONFIG = {
    store: {
      items: [
        { title: '销售库', key: 'member_quota', unit: '人', mod: 'sale' },
        { title: '待分配资源', key: 'waitallot_quota', unit: '人', mod: 'sale' },
        { title: '公海资源', key: 'sea_quota', unit: '人', mod: 'sale' },
        { title: '签约人数', key: 'sign_quota', unit: '人', mod: 'sale' },
        { title: '签约率', key: 'sign_percent', unit: '%', mod: 'sale' },
        { title: '服务库', key: 'serv_member_quota', unit: '人', mod: 'serv' },
        { title: '服务完成', key: 'finish_quota', unit: '人', mod: 'serv' },
        { title: '脱单人数', key: 'find_quota', unit: '人', mod: 'serv' },
        { title: '脱单比例', key: 'find_percent', unit: '%', mod: 'serv' },
        { title: '回收站', key: 'fail_quota', unit: '人', mod: 'sale' }
      ],
      filter: (item, props) => (item.mod === 'sale' && props.hasWorkPower) || (item.mod === 'serv' && props.hasServ)
    },
    my: {
      items: [
        { title: '资源总数', key: 'member_quota', unit: '人', mod: 'sale' },
        { title: '新分资源', key: 'new_quota', unit: '人', mod: 'sale' },
        { title: '签约人数', key: 'sign_quota', unit: '人', mod: 'sale' },
        { title: '签约率', key: 'sign_percent', unit: '%', mod: 'sale' },
        { title: '实际到店', key: 'inter_signup_quota', unit: '人', mod: 'sale' },
        { title: '服务总数', key: 'serv_member_quota', unit: '人', mod: 'serv' },
        { title: '服务完成', key: 'finish_quota', unit: '人', mod: 'serv' },
        { title: '脱单人数', key: 'find_quota', unit: '人', mod: 'serv' },
        { title: '脱单比例', key: 'find_percent', unit: '%', mod: 'serv' }
      ],
      filter: (item, props) => (item.mod === 'sale' && (props.hasSale || !props.hasServ)) || (item.mod === 'serv' && props.hasServ)
    }
  };

  const props = defineProps({
    type: { type: String, required: true, validator: v => ['store', 'my'].includes(v) },
    viewTypeText: { type: String, required: true },
    dayText: { type: String, required: true },
    data: { type: Object, required: true },
    hasSale: { type: Boolean, default: false },
    hasServ: { type: Boolean, default: false },
    hasWorkPower: { type: Boolean, default: false },
    show: { type: Boolean, default: true }
  });

  const config = STATS_CONFIG[props.type];
  const title = computed(() => `${props.viewTypeText}数据 / ${props.dayText}`);
  const visibleItems = computed(() => (props.show ? config.items.filter(item => config.filter(item, props)) : []));
  const showComponent = computed(() => visibleItems.value.length > 0);
</script>

<style lang="scss" scoped>
  .statistics_row {
    padding: 64rpx 0;
    border-bottom: 1rpx solid #f7f7f9;

    .title_bold {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      margin-bottom: 48rpx;
    }

    .statistics_list {
      @include grid(3, 100rpx, 24rpx);
      padding: 0 16rpx;

      &_item {
        @include flex-column;

        .title {
          color: #8898b6;
          font-size: 24rpx;
          margin-bottom: 4rpx;
        }

        .value {
          display: flex;
          align-items: flex-end;

          .num {
            color: #000;
            font-size: 40rpx;
            font-weight: 550;
          }

          .unit {
            color: #000;
            font-size: 24rpx;
            margin-bottom: 6rpx;
          }
        }
      }
    }
  }
</style>
