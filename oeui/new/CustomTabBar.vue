<template>
  <view class="custom-tabbar">
    <view v-for="(item, index) in tabList" :key="index" class="tab-item" @click="handleTabClick(index, item)">
      <view class="icon-container">
        <image :src="current === index ? item.selectedIcon : item.icon" class="icon" :class="{ active: current === index }" />
      </view>
      <text class="text" :class="{ 'active-text': current === index }">
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue';

const props = defineProps({
  current: {
    type: Number,
    default: 0
  }
});

const tabList = ref([
  {
    text: '工作台',
    pagePath: '/pages/index/index',
    icon: '/assets/tabbar/work.svg',
    selectedIcon: '/assets/tabbar/work_on.svg'
  },
  {
    text: '我的资源',
    pagePath: '/pages/sale/mymember',
    icon: '/assets/tabbar/resource.svg',
    selectedIcon: '/assets/tabbar/resource_on.svg'
  },
  {
    text: '我的客户',
    pagePath: '/pages/customer/index',
    icon: '/assets/tabbar/customer.svg',
    selectedIcon: '/assets/tabbar/customer_on.svg'
  },
  {
    text: '消息',
    pagePath: '/pages/message/index',
    icon: '/assets/tabbar/info.svg',
    selectedIcon: '/assets/tabbar/info_on.svg'
  },
  {
    text: '我的',
    pagePath: '/pages/profile/index',
    icon: '/assets/tabbar/my.svg',
    selectedIcon: '/assets/tabbar/my_on.svg'
  }
]);

const handleTabClick = (index, item) => {
  if (index === props.current) return;
  uni.switchTab({ url: item.pagePath });
};

onMounted(() => {
  uni.hideTabBar();
});
</script>

<style scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: white;
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.08);
  padding-bottom: calc(-20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(-20rpx + env(safe-area-inset-bottom));
  z-index: 90;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-container {
  width: 50rpx;
  height: 50rpx;
  margin: 0 0 8rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

.icon.active {
  animation: heartbeat 1.5s;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(0.8);
  }
  40% {
    transform: scale(1.1);
  }
  80%,
  100% {
    transform: scale(1);
  }
}

.text {
  font-size: 24rpx;
  line-height: 1;
  color: #000;
}

.active-text {
  color: #6365e0;
}
</style>
