<template>
  <svg class="circular" viewBox="0 0 50 50">
    <circle class="path" cx="25" cy="25" r="20" fill="none"></circle>
  </svg>
</template>

<style>
.el-loading-spinner {
  top: 50%;
  margin-top: -21px;
  width: 100%;
  text-align: center;
  position: absolute;
}
.circular {
  height: 56rpx;
  width: 56rpx;
  animation: loding-rotate 2s linear infinite;
}
.path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: #f9f9f9;
  stroke-linecap: round;
}
@keyframes loading-rotate {
  100% {
    transform: rotate (1turn);
  }
}
@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style>
