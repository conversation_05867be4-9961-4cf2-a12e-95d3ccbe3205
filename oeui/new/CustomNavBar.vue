<template>
  <view class="nav-bar" :class="{ 'nav-bar-fixed': fixed }" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <view class="nav-left" v-if="showLeft" @click="handleBack">
      <slot name="left">
        <text class="iconfont icon-fanhui"></text>
      </slot>
    </view>

    <view class="nav-title">
      <slot>{{ title }}</slot>
    </view>

    <view class="nav-right" v-if="showRight" @click="handleRightClick">
      <slot name="right">
        <text class="filter">筛选</text>
        <text class="iconfont icon-shaixuan"></text>
      </slot>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showLeft: {
    type: Boolean,
    default: false
  },
  showRight: {
    type: Boolean,
    default: false
  },
  fixed: {
    type: Boolean,
    default: false
  },
  isBack: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['back', 'rightClick']);

const safeAreaInsets = uni.getSystemInfoSync().safeAreaInsets;

const handleBack = () => {
  if (props.showLeft) {
    emit('back');
    if (props.isBack) {
      toNavigate();
    }
  }
};

const toNavigate = () => {
  let canNavBack = getCurrentPages();
  if (canNavBack && canNavBack.length > 1) {
    uni.navigateBack();
  } else {
    history.back();
  }
};

const handleRightClick = () => {
  if (props.showRight) {
    emit('rightClick');
  }
};
</script>

<style lang="scss" scoped>
.nav-bar {
  @include flex-center;
  position: relative;
  height: 92rpx;
  background: #6365e0;
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
  z-index: 999;

  &-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .nav-left,
  .nav-right {
    position: absolute;
    height: 100%;
    @include flex-center;
    padding: 0 48rpx;

    .icon-fanhui {
      font-size: 36rpx;
    }

    .icon-shaixuan {
      font-size: 48rpx;
      margin-left: 4rpx;
    }

    .filter {
      font-size: 32rpx;
    }
  }

  .nav-left {
    left: 0;
  }

  .nav-right {
    right: 0;
  }

  .nav-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .nav-title {
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
