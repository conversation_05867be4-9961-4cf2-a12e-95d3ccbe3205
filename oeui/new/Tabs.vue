<template>
  <view class="area_switch">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="area_switch_item"
      @click="handleTabClick(item)"
      :class="{
        activate: active === item.index,
        disabled: !item.enabled
      }"
    >
      {{ item.text }}
    </view>
    <view v-if="tabs.length > 1" class="underline" :style="underlineStyle"></view>
  </view>
</template>

<script setup>
  import { ref, watch, nextTick, computed } from 'vue';

  const props = defineProps({
    tabs: {
      type: Array,
      default: () => []
    },

    active: {
      type: Number,
      default: 0
    }
  });

  const emit = defineEmits(['change']);

  // 更新标签项宽度
  const itemWidths = ref([]);
  const updateItemWidths = async () => {
    await nextTick();
    const query = uni.createSelectorQuery();
    query.selectAll('.area_switch_item').boundingClientRect();
    query.exec(res => {
      if (res[0]) {
        itemWidths.value = res[0].map(item => item.width);
      }
    });
  };

  // 下划线位置计算
  const underlineStyle = computed(() => {
    const width = 16;
    let left = 0;

    for (let i = 0; i < props.active; i++) {
      left += itemWidths.value[i] || 0;
    }

    const currentItemWidth = itemWidths.value[props.active] || 0;
    left += currentItemWidth / 2 - width / 2;

    return {
      width: `${width}px`,
      transform: `translateX(${left}px)`,
      transition: 'width 300ms ease 0s, transform 300ms ease 0s'
    };
  });

  const handleTabClick = item => {
    if (!item.enabled) return;
    emit('change', item.index);
  };

  watch(
    () => props.tabs,
    () => {
      updateItemWidths();
    },
    { immediate: true, deep: true }
  );
</script>

<style lang="scss" scoped>
  .area_switch {
    position: relative;
    height: 1.1733rem;
    display: flex;
    align-items: center;

    &_item {
      max-width: 3.7333rem;
      height: 0.5867rem;
      display: block;
      align-items: center;
      justify-content: center;
      color: #8898b6;
      font-size: 0.4267rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.disabled {
        color: #ccc !important;
        cursor: not-allowed;
      }

      &.activate {
        color: #000;
        font-weight: 700;
      }
    }

    &_item:nth-child(2) {
      padding: 0 0.64rem;
    }

    .underline {
      position: absolute;
      bottom: 0.16rem;
      left: 0;
      width: 32rpx;
      height: 4rpx;
      border-radius: 2rpx;
      background: #6365e0;
    }
  }
</style>
