<template>
  <uni-section :style="sectionStyle">
    <uni-data-select placeholder="" :localdata="localdata" @change="handleChange" :value="modelValue"></uni-data-select>
  </uni-section>
</template>

<script setup>
  import { defineProps, defineEmits, computed } from 'vue';
  import { getNowDate, getNowWeek, getNextWeek, getUpWeek, getNowMonth, getUpMonth, getNextMonth, getNowYear, getUpYear } from '@/utils/getdate';
  import { dayArray } from '@/assets/staticData';

  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: ''
    },
    localdata: {
      type: Array,
      default: () => dayArray
    },
    sectionWidth: {
      type: String,
      default: '110rpx'
    }
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  const dateHandlers = {
    1: () => [getNowDate(), getNowDate()],
    2: () => [getNowDate('up'), getNowDate('up')],
    3: getNowWeek,
    4: getUpWeek,
    5: getNowMonth,
    6: getUpMonth,
    7: getNowYear,
    8: getUpYear,
    9: () => [getNowDate('next'), getNowDate('next')],
    10: getNextWeek,
    11: getNextMonth
  };

  const handleChange = value => {
    const handler = dateHandlers[value] || (() => null);
    const timeArray = handler() || [];
    emit('update:modelValue', value);
    emit('change', { day: value, timeArray });
  };

  const sectionStyle = computed(() => ({
    width: props.sectionWidth
  }));
</script>
