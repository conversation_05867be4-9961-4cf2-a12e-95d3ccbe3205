<template>
  <div ref="chartRef" class="liquid_fill_chart"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import * as echarts from 'echarts/core';
  import 'echarts-liquidfill/src/liquidFill.js';

  const props = defineProps({
    value: {
      type: Number,
      default: 0
    }
  });

  const chartRef = ref(null);
  let chartInstance = null;

  const initChart = () => {
    if (!chartRef.value) return;

    chartInstance = echarts.init(chartRef.value);
    updateChart();
  };

  const updateChart = () => {
    if (!chartRef.value) return;
    const complete = Math.min(Math.max(props.value, 0), 100); // 0-100范围
    const option = {
      series: [
        {
          type: 'liquidFill',
          data: [complete / 100],
          radius: '100%',
          center: ['50%', '50%'],
          backgroundStyle: {
            color: '#F7F7F9',
            borderWidth: 0,
            shadowColor: 'transparent',
            shadowBlur: 0
          },
          itemStyle: {
            opacity: 0.95,
            normal: {
              shadowColor: 'transparent',
              shadowBlur: 0,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#6365E0' },
                { offset: 1, color: '#9EA0FF' }
              ])
            }
          },
          label: {
            position: ['50%', '80%'],
            formatter:  params => {
              const value = params.data * 100;
              return Number.isInteger(value) ? `${value}%` : `${value.toFixed(1)}%`;
            },
            fontSize: '18px',
            color: '#fff',
            textStyle: {
              textBorderColor: '#6365E0',
              textBorderWidth: 2,
              textBorderType: 'solid',
              textShadowColor: 'transparent',
              textShadowBlur: 0,
              textShadowOffsetX: 0,
              textShadowOffsetY: 0
            }
          },
          outline: {
            show: false
          }
        }
      ]
    };
    chartInstance.setOption(option);
  };

  const handleResize = () => {
    chartInstance?.resize();
  };

  onMounted(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    chartInstance?.dispose();
  });

  watch(
    () => props.value,
    () => {
      nextTick(() => {
        updateChart();
        chartInstance?.resize();
      });
    },
    { deep: true }
  );
</script>

<style scoped>
  .liquid_fill_chart {
    width: 100%;
    height: 100%;
  }
</style>
