<template>
  <div ref="chartRef" class="funnel"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import * as echarts from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { FunnelChart } from 'echarts/charts';
  import { TitleComponent, TooltipComponent } from 'echarts/components';
  echarts.use([TitleComponent, TooltipComponent, Funnel<PERSON>hart, CanvasRenderer]);

  const props = defineProps({
    value: {
      type: Array,
      default: () => [],
      validator: val => Array.isArray(val) && val.every(item => 'name' in item && 'value' in item)
    }
  });

  const chartRef = ref(null);
  let chartInstance = null;

  const initChart = () => {
    if (!chartRef.value) return;

    chartInstance = echarts.init(chartRef.value);
    updateChart();
  };

  const updateChart = () => {
    if (!chartRef.value) return;
    const option = {
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}'
      },
      legend: {
        enabled: false,
        show: false
      },
      label: {
        color: '#fff',
        position: 'inside'
      },
      series: [
        {
          name: '销售漏斗',
          type: 'funnel',
          sort: 'none',
          left: '2%',
          right: '2%',
          top: '5%',
          bottom: '5%',
          label: {
            normal: {
              position: 'inside',
              formatter: '{b} {c}人'
            }
          },
          emphasis: {
            position: 'inside',
            fontSize: 14
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: 'solid'
            }
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
            color: '#fff',
            borderRadius: 6,
            color: function (params) {
              var colorList = [
                '#FF6F6F',
                '#FF8757',
                '#F9B45F',
                '#F4D34F',
                '#A8ED4E',
                '#5DEA6B',
                '#0AE498',
                '#70C8FF',
                '#8FA2FF',
                '#9C8FFF',
                '#C289EE',
                '#EE88F0',
                '#FF81C2',
                '#FF6D92'
              ];
              return colorList[params.dataIndex];
            }
          },
          data: props.value
        }
      ]
    };
    chartInstance.setOption(option);
  };

  watch(
    () => props.value,
    () => {
      nextTick(() => {
        updateChart();
        chartInstance?.resize();
      });
    },
    { deep: true }
  );

  onMounted(() => {
    initChart();
    window.addEventListener('resize', () => chartInstance?.resize());
  });

  onUnmounted(() => {
    window.removeEventListener('resize', () => chartInstance?.resize());
    chartInstance?.dispose();
  });
</script>

<style scoped>
  .funnel {
    width: 100%;
    height: 1000rpx;
  }
</style>
