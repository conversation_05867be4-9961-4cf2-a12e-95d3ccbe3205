<template>
  <view class="node" :class="{ selected: node.selected }" :style="{ paddingLeft }" @click="handleClick">
    <view class="label" :class="labelClasses">
      {{ node.rolename }}
    </view>
    <view v-show="node.selected" class="selected-icon">
      <text class="iconfont icon-xuanzhong"></text>
    </view>
  </view>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  });

  const paddingLeft = computed(() => `${props.level * 32}rpx`);
  const labelClasses = computed(() => ({
    [`level_${props.level}`]: true
  }));

  const emit = defineEmits(['select-node']);

  const handleClick = () => {
    emit('select-node', props.node);
  };
</script>

<style lang="scss" scoped>
  .node {
    display: flex;
    align-items: center;
    height: 86rpx;
    padding: 0 40rpx;
    cursor: pointer;

    &.selected {
      background: #f3f3fd;
      .label {
        color: #6365e0;
      }
    }

    .label {
      flex: 1;
      font-size: 32rpx;
      color: #666;
      user-select: none;
      text-align: left;
      padding-left: 32rpx;
    }

    .level_1,
    .level_2,
    .level_3 {
      color: #000;
    }

    .selected-icon {
      .icon-xuanzhong {
        font-size: 32rpx;
        color: #6365e0;
      }
    }
  }
</style>
