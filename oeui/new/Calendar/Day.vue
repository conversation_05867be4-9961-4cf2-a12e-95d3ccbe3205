<template>
  <view class="calendar_popup__day" :class="selfRangeClass" :style="firstDayStyle" @click="handleDayClick">
    <view class="calendar_popup__box" :class="selfClass">
      <view class="calendar_popup__day-solar">{{ day }}</view>
    </view>
  </view>
</template>

<script setup>
  import { CALENDAR_KEY } from './useCalendar';
  import { computed, inject } from 'vue';

  const props = defineProps({
    // 年、月
    item: {
      type: Object,
      required: true,
      validator: val => val.year && Number.isInteger(val.month)
    },
    //天数
    day: {
      type: Number,
      required: true,
      validator: val => val >= 1 && val <= 31
    },
    // 选中日期
    selectedDate: [Date, Array],
    // 类型
    type: {
      type: String,
      validator: val => ['disabled', 'selected', 'middle', 'start', 'end'].includes(val)
    }
  });

  const { handlerSelect } = inject(CALENDAR_KEY);

  // 计算首日偏移量
  const firstDayStyle = computed(() => {
    if (props.day !== 1) return null;
    const firstDay = new Date(props.item.year, props.item.month, 1).getDay();
    const offset = ((firstDay - 1 + 7) % 7) * 14.285;
    return { marginLeft: `${offset}%` };
  });

  // 状态类型
  const selfClass = computed(() => {
    const date = new Date(props.item.year, props.item.month, props.day);
    const day = date.getDay();

    return {
      // 0=周日,1=周一
      [`is-weekend-${day}`]: day === 0 || day === 1,
      // 禁用
      'is-disabled': props.type === 'disabled',
      // 选中
      'is-selected': props.type === 'selected',
      // 中间
      'is-middle': props.type === 'middle',
      // 开始
      'is-selected is-start': props.type === 'start',
      // 结束
      'is-selected is-end': props.type === 'end'
    };
  });

  const selfRangeClass = computed(() => {
    return {
      'is-selected': props.type === 'start',
      'is-selected is-start': props.type === 'start' && props.selectedDate.length > 1,
      'is-selected is-end': props.type === 'end' && props.day !== 1
    };
  });

  const handleDayClick = () => {
    if (props.type === 'disabled') return;

    const date = new Date(props.item.year, props.item.month, props.day);
    date.setHours(0, 0, 0, 0);
    handlerSelect(date);
  };
</script>

<style></style>
