// 获取每月天数(移动端需要处理一下闰年)
export const getDaysPerMonths = (year) => {
    //定义每个月的天数，如果是闰年第二月改为29天
    let daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    //4年一闰，100年不闰，400年一闰。
    if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {
        daysInMonth[1] = 29
    }
    return daysInMonth;
};

// 获取最小到最大之间的日期数据
export const getCalendarData = (minDate, maxDate) => {
    // 先获取年间隔
    let len = maxDate.getFullYear() - minDate.getFullYear();
    let res = [];
    // 根据年间隔决定创建几年数据，没有间隔也需要创建当年的数据
    for (let i = 0; i <= len; i++) {
        // 获取每一年的数据
        let year = i + minDate.getFullYear();
        let daysPerMonths = getDaysPerMonths(year);
        // 截掉最后一年后面多余的数据(如果是同一年先截掉后面的数据，保证前面索引不发生变化)
        if (i === len) {
            daysPerMonths.splice(maxDate.getMonth() + 1)
        }
        // 截掉第一年前面的多余数据
        if (i === 0) {
            daysPerMonths.splice(0, minDate.getMonth())
        }
        // 为每一个数据标记年月天数，方便外部使用
        let tempArr = [];
        for (let j = 0; j < daysPerMonths.length; j++) {
            let days = daysPerMonths[j];
            tempArr.push({
                year,
                // 因为前面的多余数据被截掉了，所以起使月需要加上最小日期月数来计算，后面的就可以直接用j当月索引
                month: i === 0 ? j + minDate.getMonth() : j,
                days
            })
        }
        res = res.concat(tempArr)
    }
    return res;
};


export function copyDate(dates) {
    return new Date(dates);
}

export function copyDates(dates) {
    if (Array.isArray(dates)) {
        return dates.map((date) => {
            if (date === null) {
                return date;
            }

            return copyDate(date);
        });
    }

    return copyDate(dates);
}

export const CALENDAR_KEY = 'base-Calendar';