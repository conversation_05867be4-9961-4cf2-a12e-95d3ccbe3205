<template>
  <div class="calendar_popup__body">
    <VirtualList ref="bodyRef" :dataList="calendarData" :itemHeight="380" containerHeight="400px">
      <template #item="{ item }">
        <view class="calendar_popup__month">
          <view class="calendar_popup__header">
            <view class="calendar_popup__month-title">
              {{ `${item.year}年${item.month + 1}月` }}
            </view>
            <view class="calendar_popup__weekdays">
              <view v-for="(item, index) in weekdays" :key="index" class="calendar_popup__weekday">
                {{ item }}
              </view>
            </view>
          </view>

          <view class="calendar_popup__days">
            <Day v-for="day in item.days" :key="day" :selectedDate="selectedDate" :item="Object.freeze(item)" :day="day" :type="getDayType(item, day)" />
          </view>
        </view>
      </template>
    </VirtualList>
  </div>
</template>

<script setup>
  import { computed, ref, onMounted } from 'vue';
  import { getCalendarData } from './useCalendar';
  import Day from './Day';
  import VirtualList from '../VirtualList';

  const props = defineProps({
    // 选择类型
    type: String,
    // 生成的最小日历
    minDate: Date,
    // 生成的最大日历
    maxDate: Date,
    // 当前选中的日期
    selectedDate: [Date, Array],
    // 日期区间最多可选天数
    maxRange: [Number, String]
  });

  const weekdays = ref(['一', '二', '三', '四', '五', '六', '日']);

  // 日历展示的数据
  const calendarData = computed(() => {
    return getCalendarData(props.minDate, props.maxDate);
  });

  // 获取每一天的类型
  const getDayType = (item, day) => {
    const timeStamp = +new Date(item.year, item.month, day);
    // 超过可选日期范围禁用
    if (timeStamp < props.minDate.getTime() || timeStamp > props.maxDate.getTime()) {
      return 'disabled';
    }
    if (!props.selectedDate) return;

    // 选择数据时的类型
    if (props.type === 'single') {
      return props.selectedDate.getTime() === timeStamp ? 'selected' : '';
    } else if (props.type === 'range') {
      let res;
      for (let i = 0; i < props.selectedDate.length; i++) {
        if (props.selectedDate[i].getTime() === timeStamp) {
          res = i === 0 ? 'start' : 'end';
        }
      }
      if (props.selectedDate.length === 2) {
        if (timeStamp > props.selectedDate[0].getTime() && timeStamp < props.selectedDate[1].getTime()) {
          return 'middle';
        }
      }
      return res;
    }
  };

  const bodyRef = ref();

  // 挂载时设置初始位置(初始位置定在选中最小日期月份位置上)
  onMounted(() => {
    if (!props.selectedDate) return;
    const miniSort = selectedDate => {
      // 获得选中的最小日期
      return [].concat(selectedDate).sort(function (a, b) {
        return a.getTime() - b.getTime();
      })[0];
    };

    let minDate = Array.isArray(props.selectedDate) ? miniSort(props.selectedDate) : props.selectedDate;
    // 选中的最小日期年减去可选的最小日期年得到他们的间隔
    let yearInterval = minDate.getFullYear() - props.minDate.getFullYear();
    // 月间隔也一样
    let monthInterval = minDate.getMonth() - props.minDate.getMonth();
    // 年间隔乘12加上月间隔得到月间隔的总数(这个总数刚好和ui的月模块索引值对应上)
    let totalMonths = yearInterval * 12 + monthInterval;
    bodyRef.value.scrollTo(totalMonths * 380 - 150);
  });
</script>

<style></style>
