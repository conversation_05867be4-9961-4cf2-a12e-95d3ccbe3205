<template>
  <PopupContainer ref="popup" title="请选择" showClose closeText="重置" @reset="reset" @confirm="handlerConfirm" @close="close">
    <template #content>
      <view class="calendar_popup">
        <view class="calendar_popup_tabs">
          <Tabs v-model="activeIndex" :tabs="tabs" @change="handleTabChange" />
        </view>
        <Body :type="type" :minDate="minDate" :maxDate="maxDate" :selectedDate="selectedDate" :maxRange="maxRange" />
      </view>
    </template>
  </PopupContainer>
</template>

<script setup>
  import { reactive, ref, defineExpose, computed, provide, toRefs } from 'vue';
  import Body from './Body.vue';
  import { CALENDAR_KEY, copyDates } from './useCalendar';
  import PopupContainer from '../PopupContainer';
  import Tabs from '../TabsSlider';

  const props = defineProps({
    type: {
      type: String,
      default: 'single'
    },
    defaultDate: {
      type: [Date, Array],
      default: () => new Date(new Date().setHours(0, 0, 0, 0))
    },
    allowSameDay: Boolean,
    maxRange: [Number, String]
  });

  const popup = ref(null);

  // 日历最小时间、最大时间
  const now = new Date();
  const minDate = ref(new Date(now.getFullYear(), now.getMonth() - 50, now.getDate()));
  const maxDate = ref(new Date(now.getFullYear(), now.getMonth() + 60, now.getDate()));

  // Tabs
  const activeIndex = ref(0);
  const tabs = ref(['分配时间', '录入时间', '最后跟进', '下次跟进']);

  const handleTabChange = index => {
    activeIndex.value = index;
  };

  const getInitialDate = () => {
    return props.type !== 'single' && !Array.isArray(props.defaultDate) ? (props.defaultDate ? [props.defaultDate] : []) : props.defaultDate;
  };

  // Emits 定义
  const emit = defineEmits(['confirm']);

  // 响应式状态
  const state = reactive({
    selectedDate: getInitialDate()
  });

  // 计算属性
  const isDisable = computed(() => {
    if (!state.selectedDate || (props.type === 'multiple' && !state.selectedDate.length) || (props.type === 'range' && state.selectedDate.length < 2)) {
      return true;
    }
    return false;
  });

  // 处理日期选择
  const handlerSelect = date => {
    // 单选
    if (props.type === 'single') {
      state.selectedDate = date;
    } else if (props.type === 'range') {
      // 区间选择
      let range = state.selectedDate.slice();

      if (range.length === 1) {
        // 第二个小于第一个直接删掉第一个, 不允许同一天也会删除第一个
        if (date.getTime() < range[0].getTime() || (!props.allowSameDay && date.getTime() === range[0].getTime())) {
          range.shift();
        } else {
          let timeStampRange = date.getTime() - range[0].getTime();
          let timeStampDay = 1000 * 60 * 60 * 24;
          if (timeStampRange / timeStampDay > props.maxRange) {
            uni.showToast({ title: `选择天数不能超过${props.maxRange}天`, icon: 'none' });
            // 结束日期设置成最大允许日期
            date = new Date(range[0].getTime() + timeStampDay * props.maxRange);
          }
        }
      } else if (range.length > 1) {
        // 已经有两个值直接清空数组
        range.length = 0;
      }

      range.push(date);
      state.selectedDate = range;
    }
  };

  const reset = () => {
    state.selectedDate = getInitialDate();
  };

  const open = async () => {
    popup.value?.open('bottom');
  };

  const close = () => {
    popup.value?.close();
  };

  const handlerConfirm = () => {
    if (isDisable.value) {
      return uni.showToast({ title: '请选择完整时间', icon: 'none' });
    }

    emit('confirm', copyDates(state.selectedDate));
    close();
  };

  provide(CALENDAR_KEY, {
    handlerSelect
  });

  defineExpose({
    reset,
    open,
    close
  });

  const { selectedDate } = toRefs(state);
</script>

<style lang="scss">
  ::v-deep .calendar_popup {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
    display: flex;
    flex-direction: column;
    height: 100%;
    color: #0c0c0c;
    background-color: #fff;
    user-select: none;

    &_tabs {
      margin-bottom: 16rpx;
    }

    &__weekdays {
      display: flex;
      padding-bottom: 16rpx;
    }

    &__weekday {
      flex: 1;
      font-size: 28rpx;
      color: #000;
      text-align: center;
    }

    &__body {
      flex: 1;
      overflow-x: hidden;
    }

    &__month-title {
      text-align: left;
      font-weight: 500;
      font-size: 32rpx;
      color: #000;
      padding-bottom: 32rpx;
      padding-left: 6rpx;
    }

    &__days {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx 0;
    }

    &__day.is-selected.is-start {
      background: linear-gradient(to right, transparent, rgba(#6365e0, 0.08));
    }

    &__day.is-selected.is-end {
      background: linear-gradient(to left, transparent, rgba(#6365e0, 0.08));
    }

    &__day {
      position: relative;
      width: 14.285%;
      height: 82rpx;
      box-sizing: border-box;
      cursor: pointer;
      text-align: center;

      &-solar {
        width: 100%;
        height: 100%;
        font-size: 28rpx;
        @include flex-center;
      }
    }

    &__box {
      width: 100%;
      max-width: 82rpx;
      height: 82rpx;
      display: inline-block;
      border-radius: 16rpx;
      color: #000;

      &.is-disabled {
        opacity: 0.32;
      }

      &.is-selected {
        background: #6365e0;
        color: #fff;
      }

      &.is-middle {
        max-width: 100%;
        border-radius: 0;
        color: #000;
        background: rgba($color: #6365e0, $alpha: 0.08);
      }

      &.is-weekend-1.is-middle {
        border-bottom-left-radius: 16rpx;
        border-top-left-radius: 16rpx;
      }

      &.is-weekend-0.is-middle {
        border-bottom-right-radius: 16rpx;
        border-top-right-radius: 16rpx;
      }
    }

    &__month {
      padding-bottom: 24rpx;
    }
  }
</style>
