<template>
  <view class="mobile-display">
    <text class="label">手机号：</text>
    <text class="number" :class="{ auto: isVerified }">
      {{ mobileView || '--' }}
    </text>

    <!-- 查看/隐藏手机号按钮 -->
    <template v-if="showToggleButton">
      <text class="iconfont" :class="isCompleteMobile ? 'icon-kejian' : 'icon-biyan'" @click="toggleMobileDisplay"></text>
    </template>

    <!-- 认证标识 -->
    <text v-if="isVerified" class="iconfont icon-renzheng1 auto"></text>
  </view>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { useStore } from 'vuex';
  import { postViewmobile } from '@/api/common.js';

  const store = useStore();

  const props = defineProps({
    mobile: {
      type: String,
      default: ''
    },
    mobileInfo: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: [String, Number],
      default: null
    }
  });

  watch(
    () => props.mobile,
    mobile => {
      mobileView.value = mobile;
    },
    { deep: true }
  );

  const isCompleteMobile = ref(false);
  const viewPower = ref(null);
  const mobileView = ref('');

  // 认证状态
  const isVerified = computed(() => props.mobileInfo.mobilerz === '1');
  const showToggleButton = computed(() => {
    return viewPower.value !== 1 && mobileView && mobileView !== '--' && props.id && props.mobile && props.mobile.includes('*');
  });

  const toggleMobileDisplay = () => {
    if (isCompleteMobile.value) {
      hideMobile();
    } else {
      showMobile();
    }
  };

  const showMobile = async () => {
    if (viewPower.value === 0) {
      uni.showToast({ title: '今天查看次数已用完', icon: 'none' });
      return;
    }

    if (!props.id) {
      uni.showToast({ title: '资源ID为空', icon: 'none' });
      return;
    }

    try {
      const res = await postViewmobile({
        a: 'get',
        id: props.id,
        type: 'member'
      });

      if (res.ret === 1) {
        isCompleteMobile.value = true;
        mobileView.value = res.result.mobile;
      } else if (res.ret !== 0) {
        uni.showToast({
          title: res.msg || '系统繁忙，请稍后重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const hideMobile = () => {
    mobileView.value = props.mobile;
    isCompleteMobile.value = false;
  };

  onMounted(() => {
    mobileView.value = props.mobile;
    const vuexData = parseJson(uni.getStorageSync('vuex') || '{}');
    const loginInfo = store.state?.loginInfo || vuexData?.loginInfo;
    viewPower.value = loginInfo?.mobiel_power;
  });

  const parseJson = str => {
    try {
      return JSON.parse(str);
    } catch {
      return {};
    }
  };
</script>
<style lang="scss" scoped>
  .mobile-display {
    @include flex-center;
    justify-content: flex-start;
    color: #000;
    font-size: 32rpx;
    line-height: 1;

    .iconfont {
      font-size: 36rpx;
      color: #000;
    }

    .number {
      margin-right: 16rpx;
    }

    .auto {
      color: #0fc6c2;
    }

    .icon-renzheng1 {
      margin-left: 16rpx;
      color: #0fc6c2;
    }
  }
</style>
