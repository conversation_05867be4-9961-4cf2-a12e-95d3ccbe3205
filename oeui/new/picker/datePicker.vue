<template>
  <view class="range_option">
    <view class="range_option_title">{{ title }}</view>
    <view class="range_option_content" @click="open">
      <text v-if="!returnTime[1]">{{ placeholder }}</text>
      <text class="value" v-else>{{ formatDate(returnTime[0]) }}——{{ formatDate(returnTime[1]) }}</text>
      <text class="iconfont icon-youjiantou1" :class="{ value: returnTime[1] }"></text>
    </view>
  </view>
  <PopupContainer ref="popup" title="请选择" showClose closeText="重置" @reset="reset" @confirm="handlerConfirm" @close="close">
    <template #content>
      <scroll-view :scroll-x="true" @touchmove.stop @scroll="onScroll" class="scroll-view-box">
        <view class="flex_dc flex_jsb pr">
          <view class="time_data" :class="{ active_time: day == item.value }" v-for="item in dayArray" :key="item.value" @click="setTime(item)">{{ item.text }}</view>
          <view class="mark_box" v-show="markShow"></view>
        </view>
      </scroll-view>
      <view class="flex_dc flex_jsb mt16 mb16">
        <view class="btn_per fz16">{{ dayjs(state.selectedDate[0]).format('YYYY年MM月DD日') }}</view>
        <view class="font-info">—</view>
        <view class="btn_per fz16">
          {{ state.selectedDate[1] ? dayjs(state.selectedDate[1]).format('YYYY年MM月DD日') : dayjs(state.selectedDate[0]).format('YYYY年MM月DD日') }}
        </view>
      </view>
      <view class="calendar_popup">
        <Body v-if="bodyReset" :type="type" :minDate="minDate" :maxDate="maxDate" :selectedDate="selectedDate" />
      </view>
    </template>
  </PopupContainer>
</template>
<script setup>
import { ref, reactive, toRefs, provide, nextTick, defineEmits, watch, onMounted } from 'vue';
import PopupContainer from '../PopupContainer.vue';
import dayjs from 'dayjs';
import { getNowDate, getNowWeek, getNextWeek, getUpWeek, getNowMonth, getUpMonth, getNextMonth, getNowYear, getUpYear } from '@/utils/getdate';
import { CALENDAR_KEY } from '../Calendar/useCalendar';
import Body from '../Calendar/Body.vue';
const popup = ref();
const day = ref('1');
const dayArray = ref([
  { text: '今天', value: '1' },
  { text: '昨天', value: '2' },
  { text: '本周', value: '3' },
  { text: '上周', value: '4' },
  { text: '本月', value: '5' },
  { text: '上月', value: '6' },
  { text: '今年', value: '7' },
  { text: '去年', value: '8' }
]);
const props = defineProps({
  title: {
    type: String,
    default: '时间'
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  type: {
    type: String,
    default: 'single'
  },
  defaultDate: {
    type: [Date, Array],
    default: ''
  },
  allowSameDay: Boolean,
  maxRange: [Number, String]
});
const emit = defineEmits(['confirm']);
// 日历最小时间、最大时间
const now = new Date();
const minDate = ref(new Date(now.getFullYear(), now.getMonth() - 50, now.getDate()));
const maxDate = ref(new Date(now.getFullYear(), now.getMonth() + 60, now.getDate()));

// 封装日期格式化函数
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD');
};
const getInitialDate = () => {
  return props.type !== 'single' && !Array.isArray(props.defaultDate) ? (props.defaultDate ? [props.defaultDate] : [new Date(new Date().setHours(0, 0, 0, 0))]) : props.defaultDate.map(item => dayjs(item).$d);
};
// 处理日期选择
const handlerSelect = date => {
  // 单选
  if (props.type === 'single') {
    state.selectedDate = date;
  } else if (props.type === 'range') {
    // 区间选择
    let range = state.selectedDate.slice();

    if (range.length === 1) {
      // 第二个小于第一个直接删掉第一个, 不允许同一天也会删除第一个
      if (date.getTime() < range[0].getTime() || (!props.allowSameDay && date.getTime() === range[0].getTime())) {
        range.shift();
      } else {
        let timeStampRange = date.getTime() - range[0].getTime();
        let timeStampDay = 1000 * 60 * 60 * 24;
        if (timeStampRange / timeStampDay > props.maxRange) {
          uni.showToast({
            title: `选择天数不能超过${props.maxRange}天`,
            icon: 'none'
          });
          // 结束日期设置成最大允许日期
          date = new Date(range[0].getTime() + timeStampDay * props.maxRange);
        }
      }
    } else if (range.length > 1) {
      // 已经有两个值直接清空数组
      range.length = 0;
    }
    range.push(date);
    state.selectedDate = range;
  }
};
provide(CALENDAR_KEY, {
  handlerSelect
});
// 响应式状态
const state = reactive({
  selectedDate: getInitialDate()
});
const { selectedDate } = toRefs(state);
//设置右滑蒙版
const markShow = ref(true);
const onScroll = e => {
  if (e.detail.scrollLeft < 215) {
    markShow.value = true;
  } else {
    markShow.value = false;
  }
};
//
const bodyReset = ref(true);
const setTime = async item => {
  bodyReset.value = false;
  const val = item.value;
  let selectedDate;
  if (val == 1) {
    selectedDate = [getNowDate(), getNowDate()];
  } else if (val == 2) {
    selectedDate = [getNowDate('up'), getNowDate('up')];
  } else if (val == 3) {
    selectedDate = getNowWeek();
  } else if (val == 4) {
    selectedDate = getUpWeek();
  } else if (val == 5) {
    selectedDate = getNowMonth();
  } else if (val == 6) {
    selectedDate = getUpMonth();
  } else if (val == 7) {
    selectedDate = getNowYear();
  } else if (val == 8) {
    selectedDate = getUpYear();
  }
  state.selectedDate = selectedDate.map(item => dayjs(item).$d);
  day.value = val;
  await nextTick();
  bodyReset.value = true;
};
const allDays = ref([[getNowDate(), getNowDate()], [getNowDate('up'), getNowDate('up')], getNowWeek(), getUpWeek(), getNowMonth(), getUpMonth(), getNowYear(), getUpYear()]);
const open = async () => {
  await nextTick();
  if (popup.value && typeof popup.value.open === 'function') {
    popup.value.open();
  }
};
const reset = () => {
  state.selectedDate = getInitialDate();
  returnTime.value = [];
  handlerSelect(new Date(new Date().setHours(0, 0, 0, 0)));
};
const returnTime = ref([]);
const close = () => {
  popup.value.close();
};
const handlerConfirm = () => {
  if(state.selectedDate.length>1){
  returnTime.value = state.selectedDate.map(item=>item);}
  else{
    returnTime.value = [state.selectedDate,state.selectedDate]
  }
  emit('confirm', returnTime.value);
  close();
};
watch(
  () => state.selectedDate,
  nval => {
    if(!props.defaultDate||!nval.length) return
    const dateTime = JSON.parse(JSON.stringify(state.selectedDate));
    if (dateTime && dateTime.length == 1) {
      returnTime.value = [dateTime[0], dateTime[0]];
    } else {
      returnTime.value = dateTime.map(item => {
        return item;
      });
    }
    day.value = '';
    for (let i = 0; i < allDays.value.length; i++) {
      const item = allDays.value[i];
      if (returnTime.value && returnTime.value.length == 1) {
        if (item[0] == formatDate(returnTime.value[0]) && item[1] == formatDate(returnTime.value[0])) {
          day.value = i + 1;
          return
        }
      } else if (returnTime.value && returnTime.value.length == 2) {
        if (item[0] == formatDate(returnTime.value[0]) && item[1] == formatDate(returnTime.value[1])) {
          day.value = i + 1;
          return
        }
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.scroll-view-box {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.scroll-view-box ::-webkit-scrollbar {
  display: none;
}
.range_option {
  @include flex-between;
  background-color: #fff;

  &.disabled {
    opacity: 0.5;
  }

  &_title {
    width: 160rpx;
    font-size: 32rpx;
    color: #000;
  }

  &_content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    height: 100%;

    text.value {
      color: #373d49;
    }

    text {
      color: #c0c4cc;
      margin-right: 12rpx;
      font-size: 32rpx;
    }
  }
}
.time_data {
  /* 自动布局 */
  font-family: PingFang SC;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;
  padding: 12rpx 32rpx;
  white-space: nowrap;
  margin: 0 10rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  /* 辅助线灰 */
  border: 1px solid #dcdfe6;
}
.active_time {
  background: #6365e0;
  color: #fff;
}
.mark_box {
  position: -webkit-sticky; /* 为 Safari 兼容性 */
  position: sticky;
  right: -1px; /* 当 .sticky-element 到达距离父 div 顶部 10px 时固定 */
  background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  padding: 32rpx 40rpx;
}
::v-deep .calendar_popup {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  display: flex;
  flex-direction: column;
  height: 100%;
  color: #0c0c0c;
  background-color: #fff;
  user-select: none;

  &_tabs {
    margin-bottom: 16rpx;
  }

  &__weekdays {
    display: flex;
    padding-bottom: 16rpx;
  }

  &__weekday {
    flex: 1;
    font-size: 28rpx;
    color: #000;
    text-align: center;
  }

  &__body {
    flex: 1;
    overflow-x: hidden;
  }

  &__month-title {
    text-align: left;
    font-weight: 500;
    font-size: 32rpx;
    color: #000;
    padding-bottom: 32rpx;
    padding-left: 6rpx;
  }

  &__days {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx 0;
  }

  &__day.is-selected.is-start {
    background: linear-gradient(to right, transparent, rgba(#6365e0, 0.08));
  }

  &__day.is-selected.is-end {
    background: linear-gradient(to left, transparent, rgba(#6365e0, 0.08));
  }

  &__day {
    position: relative;
    width: 14.285%;
    height: 82rpx;
    box-sizing: border-box;
    cursor: pointer;
    text-align: center;

    &-solar {
      width: 100%;
      height: 100%;
      font-size: 28rpx;
      @include flex-center;
    }
  }

  &__box {
    width: 100%;
    max-width: 82rpx;
    height: 82rpx;
    display: inline-block;
    border-radius: 16rpx;
    color: #000;

    &.is-disabled {
      opacity: 0.32;
    }

    &.is-selected {
      background: #6365e0;
      color: #fff;
    }

    &.is-middle {
      max-width: 100%;
      border-radius: 0;
      color: #000;
      background: rgba($color: #6365e0, $alpha: 0.08);
    }

    &.is-weekend-1.is-middle {
      border-bottom-left-radius: 16rpx;
      border-top-left-radius: 16rpx;
    }

    &.is-weekend-0.is-middle {
      border-bottom-right-radius: 16rpx;
      border-top-right-radius: 16rpx;
    }
  }

  &__month {
    padding-bottom: 24rpx;
  }
}
</style>
