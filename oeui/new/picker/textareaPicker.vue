<template>
  <view class="textarea-picker-container">
    <view class="fz16 pr" :class="className">
      <view class="font-s mb12"
        >{{ title }}<text class="color_error" v-if="isMust">*</text></view
      >
      <textarea
        class="textarea"
        v-model="text"
        :style="style"
        :maxlength="maxlength"
        placeholder="请输入"
        placeholder-class="place_class"
        @blur="blur"
        border
      ></textarea>
      <view class="count">{{ text.length || 0 }}/{{ maxlength }}</view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "取消原因",
  },
  modelValue: {
    type: String || Number,
    default: "",
  },
  isMust: {
    type: Boolean,
    default: false,
  },
  style: {
    type: Object,
  },
  maxlength: {
    type: Number,
    default: 200,
  },
  className:{
    type: String,
    default: ''
  }
});
const text = ref("");
const emit = defineEmits(["update:modelValue", "change"]);

const blur = (e) => {
  emit("change", e.detail.value);
  emit("update:modelValue", e.detail.value);
};
</script>

<style scoped>
.textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #ccc;
  border-radius: 10rpx;
  padding: 20rpx;
  box-sizing: border-box;
  resize: none;
}
.count {
  font-size: 14px;
  position: absolute;
  bottom: 10rpx;
  right: 20rpx;
  color: #999;
}
.place_class{
  color: #C0C4CC;
}
.font-s {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 主标题色 */
  color: #000000;
}
</style>
