<template>
  <view class="datetime-picker-container">
    <view class="picker-header">
      <text class="title">{{ title }}</text>
      <view class="datetime-picker picker-header">
        <uni-datetime-picker
          :type="type"
          v-model="selectedTime"
          :border="false"
          @change="handleTimeChange"
        />
        <text class="iconfont icon-zhankai-011 rotate90"></text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { nextTick, onMounted, ref,watch } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "更改时间",
  },
  initialTime: {
    type: String || Number,
    default: "",
  },
  type: {
    type: String,
    default: "datetime",
  },
});
onMounted(async () => {
  await nextTick();
  selectedTime.value = props.initialTime || new Date().toISOString();
});
watch(
  () => props.initialTime,
  (nval,oval) => {
    selectedTime.value = nval || new Date().toISOString();
  }
);
const selectedTime = ref();
const emit = defineEmits(["timeChanged"]);
const handleTimeChange = (e) => {
  selectedTime.value = e;
  // 如果需要，可以在这里触发一个事件来通知父组件时间已更改
  emit("timeChanged", selectedTime.value);
};
</script>

<style scoped>
.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .uniui-calendar {
  display: none;
}
::v-deep .uni-date__x-input {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  text-align: right;
  letter-spacing: 0px;

  /* 副标色 */
  color: #666666;
}
.title {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 主标题色 */
  color: #000000;
}
.rotate90 {
  transform: rotate(-90deg);
  color: #666666;
}
.selected-time {
  font-size: 14px;
  color: #888;
}
.datetime-picker {
  text-align: right;
}
</style>
