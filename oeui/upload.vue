<template>
  <div class="oe_upload">
    <div class="upload" @click="upload" :style="{width:size,height:size}">
      <i class="oeuifont oeui-jia"></i>
      <input ref="uploadImg" type="file" accept="image/*" @change="changeUpload($event)" multiple="multiple"
        style="display:none;">
    </div>
    <div class="preview" v-for="(item, i) in listImg" :key="i"  :style="{width:size,height:size}">
      <img :src="item.drawimg_url">
      <span class="remove" @click="removeImg(i)">
        <i class="oeuifont oeui-guanbi1"></i>
      </span>
    </div>

  </div>
</template>
<script setup>
import { getCurrentInstance, ref,watch } from 'vue';
import { commonUpload } from '@/api/member'
const emit = defineEmits(['change', 'update:modelValue', 'del'])
const props = defineProps({
  size: {
    type: String,
    default: '54px',
  },
  limit: {
    type: Number,
    default: 9,
  },
  module: {
    type: String,
    default: 'member',
  },
  value: {
    type:Array,
    default:[]
  },
})
const { proxy } = getCurrentInstance();

let listImg = ref([]);


const upload = () => {
  proxy.$refs.uploadImg.dispatchEvent(new MouseEvent('click'));
}
const changeUpload = (e) => {
  let inputFile = Array.from(e.target.files);
  sendUpload(inputFile)
}
const sendUpload = async (file) => {
  if (file.length == 0) return
  if ((file.length + listImg.value.length) > props.limit) {
      proxy.OEUI.toast({
        text:'上传图片数量已满'
      })
    return
  }
  proxy.OEUI.loading.show();//显示提示
  for(let i=0; i<file.length; i++){
    let res = await commonUpload({ uploadpart: file[i], module: props.module, thumb: 1 })
    if(res.ret == 1){
      listImg.value.push(res.result)
      emit('change', listImg.value)
      emit('update:modelValue', listImg.value)
    }else{
      proxy.OEUI.toast({
        text:'上传失败'
      })
    }
  }
  proxy.OEUI.loading.hide();//隐藏提示
}

const removeImg = (index) => {
  listImg.splice(index, 1)
  emit('change', listImg.value)
  emit('update:modelValue', listImg.value)
}
watch(
  () => props.value,
  newCur => {
    if (newCur.length > 0 && typeof newCur == 'object') {
      listImg.value = []
      newCur.forEach((v, i) => {
        let params = {
          drawimg_url: v.drawimg_url,
          drawimg: v.drawimg,
          thumbimg_url:v.thumbimg_url,
          thumbimg: v.thumbimg,
          albumid: v.albumid,

        }
        listImg.value.push(params)
      })
    } else if (newCur.length == 0) {
      listImg.value = []
    }
  },
  { deep: true, immediate: true },
)
</script>
<style lang='scss' scoped>
@import './icon/iconfont.css';

.oe_upload {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .preview {
    position: relative;
    width: 2.133rem;
    height: 2.133rem;
    margin: 0 .213rem .213rem 0;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: .133rem;
    }

    .remove {
      position: absolute;
      right: -.133rem;
      top: -.133rem;
      width: .48rem;
      height: .48rem;
      line-height: .48rem;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.8);
      text-align: center;
      border-radius: 50%;

      i {
        font-size: .373rem;
      }
    }
  }
  // .preview:nth-child(5){
  //   margin-right: 0;
  // }

  .upload {
    width: 2.133rem;
    height: 2.133rem;
    color: #dcdee0;
    background: #f7f8fa;
    border-radius: .133rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 .213rem .213rem 0;

    .oeuifont {
      font-size: .533rem;
    }
  }
}
</style>