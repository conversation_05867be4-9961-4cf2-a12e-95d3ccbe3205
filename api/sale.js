import http from '@/utils/api.js';
const m = 'crm';

// 销售待分配资源
export const getSaleBlankUserList = params => {
  return http.post('', { m, c: 'allot', ...params });
};

//检查红娘可分配资源
export const getMakerChecked = params => {
  return http.post('', { m, c: 'allot', a: 'checkquota', ...params });
};

//分配资源
export const postAllotPer = params => {
  return http.post('', { m, c: 'allot', a: 'savepre', ...params });
};

// 到店预测列表
export const getInterlist = params => {
  return http.post('', { m, c: 'inter', ...params });
};

//邀约回访
export function postInterfollowup(params) {
  return http.post('', { m, c: 'followup', ...params });
}

//我的面谈
export function getFacetolist(params) {
  return http.post('', { m, c: 'faceto', ...params });
}

// 销售我的资源
export const getSaleSaleUserList = params => {
  return http.post('', { m, c: 'sale', ...params });
};
