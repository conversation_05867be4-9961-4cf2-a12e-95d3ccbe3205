import http from '@/utils/api.js';
const m = 'crm';

// 仪表盘数据-门店、部门
export const getWorkTotal = params => {
  return http.post('', { m, c: 'work', ...params });
};

//  仪表盘数据-本人
export const getMyWorkTotal = params => {
  return http.post('', { m, c: 'mywork', ...params });
};

// 销售漏斗
export const getSalefunnel = params => {
  return http.post('', { m, c: 'funnel1', ...params });
};

// 服务漏斗
export const getServefunnel = params => {
  return http.post('', { m, c: 'funnel2', ...params });
};

// 添加资源
export const addCrmmember = params => {
  return http.post('', { m, c: 'member', a: 'saveadd', ...params });
};

// 编辑资源
export const editCrmmember = params => {
  return http.post('', { m, c: 'member', a: 'saveedit', ...params });
};

// 用户详情（资源总库）
export const userDetail = params => {
  return http.post('', { m, c: 'member', a: 'detail', ...params });
};

// 用户详情（销售）
export const saleuserDetail = params => {
  return http.post('', { m, c: 'sale', a: 'view', ...params });
};

// 资源相册
export const postPhoto = params => {
  return http.post('', { m, c: 'photo', ...params });
};
