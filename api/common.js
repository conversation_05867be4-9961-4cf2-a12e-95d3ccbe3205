import http from '@/utils/api.js';

const vm = 'vuewap';
const cm = 'crm';

// 获取地区数据
export const getAreaList = (parame = {}) => {
  return http.post('', { m: vm, ...parame });
};

// 获取所有部门
export const getClassAll = params => {
  return http.post('', { m: cm, c: 'ajax', a: 'sitedepart', ...params });
};
//获取门店套餐
export function getPackagepicker(params) {
  return http.post('', { m: cm, c: 'ajax', a: 'voservicetc', ...params });
}

// 获取红娘
export const getCalssMaker = params => {
  return http.post('', { m: cm, c: 'ajax', a: 'twomaker', ...params });
};

// 工作助手-统计
export const initAssistant = params => {
  return http.post('', { m: cm, c: 'assistant', ...params });
};

// 查看用户手机号
export const postViewmobile = params => {
  return http.post('', { m: cm, c: 'viewmobile', ...params });
};

// 获取所有部门+红娘
export const getAllCalssMaker = params => {
  return http.post('', { m: cm, c: 'ajax', a: 'sitedepart', s_method: 'allot', type: '2', s_sitemk: 1, ...params });
};

// 根据红娘姓名/昵称/红娘ID/手机号
export const getMakerall = params => {
  return http.post('', { m: cm, c: 'ajax', a: 'listmaker', ...params });
};

//获取picker
export const postCrmPicker = params => {
  return http.post('', { m: cm, c: 'ajax', ...params });
};

// 上传图片
export const commonUpload = params => {
  return new Promise((resolve, rejct) => {
    http
      .post('?m=crm&c=upload', { a: 'image', ...params })
      .then(res => {
        if (res.ret == 1) {
          resolve(res);
        } else resolve(res);
      })
      .catch(err => {
        rejct(err);
      });
  });
};

// 验证手机号
export const verifyMobile = params => {
  return http.post('', { m: cm, c: 'member', a: 'checkmobile', ...params, noToast: true });
};

// 验证微信号
export const verifyWx = params => {
  return http.post('', { m: cm, c: 'member', a: 'checkwx', ...params, noToast: true });
};
