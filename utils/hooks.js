/**
 * 移除省市区对象中的 children 字段
 * @param {Object} data - 包含省市区三级数据的对象
 * @returns {Object} 清理后的数据对象
 */
export function removeChildren(data) {
  const cleaned = JSON.parse(JSON.stringify(data));

  const cleanNode = (node) => {
    if (!node) return null;
    delete node.children;
    if (node.value === data.province?.value && node.children) {
      node.children.forEach(cleanNode);
    }

    return node;
  };

  return {
    province: cleanNode(cleaned.province),
    city: cleanNode(cleaned.city),
    district: cleanNode(cleaned.district)
  };
}


export function debounce(fn, delay) {
  let timer;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

export function throttle(fn, limit) {
  let inThrottle;
  return function (...args) {
    const self = this;
    if (!inThrottle) {
      fn.apply(self, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

export function formatNumberWithCommas(number, unit = true, decimal = false) {
  number = Number(number)
  if (typeof number !== 'number') {
    return '0'
  }
  var parts = [];
  if (unit) {
    parts = number.toFixed(2).split('.');
  } else {
    if (decimal) {
      parts[0] = number.toFixed(2).toString();
    } else {
      parts[0] = number.toString();
    }

  }

  if (number > 10000 && unit) {
    var wan = (parts[0] / 10000).toFixed(2);
    wan = wan.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return wan
  } else {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return parts.join('.');
  }

}

export const handleEmptyValue = (value, defaultValue = '--') => {
  if (!value || (typeof value === 'string' && value === '0')) {
    return defaultValue;
  }
  return value;
};

export const getMinMaxValues = (arr) => {
  if (!arr || !Array.isArray(arr) || arr.length === 0) {
    return { min: null, max: null }; // 处理空数组或无效数据
  }

  const minValue = arr.at(0)?.value;
  const maxValue = arr.at(-1)?.value;
  return { min: minValue, max: maxValue };
};