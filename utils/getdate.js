import { number } from "echarts"

//获取当天日期
const getNowDate = type => {
  var date = new Date()
  if (type == 'up') {
    date = new Date(date.getTime() - 1000 * 60 * 60 * 24)
  } else if (type == 'next') {
    date = new Date(date.getTime() + 1000 * 60 * 60 * 24)
  }

  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m.toString().length == 1 ? '0' + m : m

  var d = date.getDate()
  d = d.toString().length == 1 ? '0' + d : d
  return y + '-' + m + '-' + d
}

//本周
function getNowWeek() {
  var today = new Date()
  var dayTime = 1000 * 60 * 60 * 24
  let currentWeek = today.getDay() == 0 ? 6 : today.getDay() //当前周
  var currentDay = currentWeek * dayTime //计算为从周一开始
  var startDate = new Date(today.getTime() - currentDay + dayTime) // 计算本周第一天的日期

  var startYear = startDate.getFullYear()
  var startMonth = ('0' + (startDate.getMonth() + 1)).slice(-2)
  var startDay = ('0' + startDate.getDate()).slice(-2)

  // 当天就是本周当前最后一天
  var endYear = today.getFullYear()
  var endMonth = ('0' + (today.getMonth() + 1)).slice(-2)
  var endDay = ('0' + today.getDate()).slice(-2)

  var startDateStr = startYear + '-' + startMonth + '-' + startDay
  var endDateStr = endYear + '-' + endMonth + '-' + endDay

  return [startDateStr, endDateStr]
}

//上周
function getUpWeek() {
  var today = new Date()
  var currentDay = today.getDay() == 0 ? 7 : today.getDay()
  var dayTime = 1000 * 60 * 60 * 24
  var currentDayTime = (currentDay + 6) * dayTime //计算为从周一开始
  var startDate = new Date(today.getTime() - currentDayTime) // 计算上周第一天的日期

  var endcurrentDay = currentDay * dayTime //计算为从周一开始
  var endDate = new Date(today.getTime() - endcurrentDay) // 计算上周结束日期

  var startYear = startDate.getFullYear()
  var startMonth = ('0' + (startDate.getMonth() + 1)).slice(-2)
  var startDay = ('0' + startDate.getDate()).slice(-2)

  // 当天就是本周当前最后一天
  var endYear = endDate.getFullYear()
  var endMonth = ('0' + (endDate.getMonth() + 1)).slice(-2)
  var endDay = ('0' + endDate.getDate()).slice(-2)

  var startDateStr = startYear + '-' + startMonth + '-' + startDay
  var endDateStr = endYear + '-' + endMonth + '-' + endDay

  return [startDateStr, endDateStr]
}
//下周
function getNextWeek() {
  // 获取当前日期
  let today = new Date();

  // 获取今天是周几（0-周日，1-周一，...，6-周六）
  let todayDay = today.getDay();

  // 计算下周的周一日期
  // 注意：getDay() 返回的是 0-6，如果要计算下周的周一，需要加上 7 - 当前是周几
  // 如果今天是周日（getDay() 返回 0），则 7 - 0 = 7，意味着我们需要加 7 天
  let nextMonday = new Date(today.getTime() + (7 - todayDay + 1) * 24 * 60 * 60 * 1000);

  // 计算下周的周日日期
  // 下周的周日就是下周的周一加上 7 天
  let nextSunday = new Date(nextMonday.getTime() + 6 * 24 * 60 * 60 * 1000);

  // 格式化日期（可选，这里简单使用 yyyy-MM-dd 格式）
  function formatDate(date) {
    let year = date.getFullYear();
    let month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
    let day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  return [formatDate(nextMonday), formatDate(nextSunday)]
}
//本月
function getNowMonth() {
  var today = new Date()
  var dayTime = 1000 * 60 * 60 * 24
  var currentDay = today.getDate() * dayTime - dayTime //获取当前日期时间戳
  var startDate = new Date(today.getTime() - currentDay) // 计算本月第一天的日期

  var startYear = startDate.getFullYear()
  var startMonth = ('0' + (startDate.getMonth() + 1)).slice(-2)
  var startDay = ('0' + startDate.getDate()).slice(-2)

  // 当天就是本月当前最后一天
  var endYear = today.getFullYear()
  var endMonth = ('0' + (today.getMonth() + 1)).slice(-2)
  var endDay = ('0' + today.getDate()).slice(-2)

  var startDateStr = startYear + '-' + startMonth + '-' + startDay
  var endDateStr = endYear + '-' + endMonth + '-' + endDay

  return [startDateStr, endDateStr]
}
//上月
function getUpMonth() {
  // 获取当前日期
  const today = new Date();

  // 获取当前月份（注意，月份是从0开始的，所以需要加1）
  const currentMonth = today.getMonth();

  let start, end = null;
  // 如果当前是一年的一月，上月就是前一年的十二月
  if (currentMonth === 0) {
    const lastYear = today.getFullYear() - 1;
    start = new Date(lastYear, 11, 1), // 上一年的12月1日
      end = new Date(lastYear, 11, 31) // 上一年的12月31日

  } else {
    // 否则，上月就是当前年份的上个月份
    start = new Date(today.getFullYear(), currentMonth - 1, 1), // 当前年的上个月1日
      end = new Date(today.getFullYear(), currentMonth, 0) // 当前年的本月0日（即上个月最后一天）

  }
  let start_month = ('0' + (start.getMonth() + 1)).slice(-2)
  let start_day = ('0' + start.getDate()).slice(-2)
  start = start.getFullYear() + '-' + start_month + '-' + start_day

  let end_month = ('0' + (end.getMonth() + 1)).slice(-2)
  let end_day = ('0' + end.getDate()).slice(-2)
  end = end.getFullYear() + '-' + end_month + '-' + end_day

  return [start, end]
}
//下月
function getNextMonth() {
  let today = new Date();
  // 获取当前年份和月份
  let currentYear = today.getFullYear();
  let currentMonth = today.getMonth() + 1; // 月份从0开始，所以需要+1

  // 计算下个月的年份和月份
  let nextMonth = currentMonth % 12 + 1; // 如果当前月份是12月，则取余数后+1变为1月
  let nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;

  // 创建下个月的第一天和最后一天的Date对象
  let nextMonthFirstDay = new Date(nextYear, nextMonth - 1, 1); // 月份需要-1，因为Date构造函数中月份从0开始
  let nextMonthLastDay = new Date(nextYear, nextMonth, 0); // 设置日期为0时，会自动变成上一个月的最后一天，然后+1就是当前月的最后一天

  // 格式化日期
  function formatDate(date) {
    let year = date.getFullYear();
    let month = String(date.getMonth() + 1).padStart(2, '0');
    let day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 返回格式化后的日期字符串
  return [formatDate(nextMonthFirstDay), formatDate(nextMonthLastDay)]
}
//本年
function getNowYear() {
  // 获取当前日期
  const today = new Date();

  let start = today.getFullYear() + '-' + '01' + '-' + '01'


  let end_month = ('0' + (today.getMonth() + 1)).slice(-2)
  let end_day = ('0' + today.getDate()).slice(-2)
  let end = today.getFullYear() + '-' + end_month + '-' + end_day;
  return [start, end]
}


//上年
function getUpYear() {
  // 获取当前日期
  const today = new Date();
  const lastYear = today.getFullYear() - 1;
  let start = new Date(lastYear, 0, 1)
  let end = new Date(lastYear, 12, 0)
  let start_month = ('0' + (start.getMonth() + 1)).slice(-2)
  let start_day = ('0' + start.getDate()).slice(-2)
  let end_month = ('0' + (end.getMonth() + 1)).slice(-2)
  let end_day = ('0' + end.getDate()).slice(-2)


  start = start.getFullYear() + '-' + start_month + '-' + start_day
  end = end.getFullYear() + '-' + end_month + '-' + end_day;
  return [start, end]
}

export {
  getNowDate,
  getNowWeek,
  getUpWeek,
  getNextWeek,
  getNowMonth,
  getUpMonth,
  getNextMonth,
  getNowYear,
  getUpYear,
}