import Request from './request';
import config from '../config.js';
const request = new Request();

// 请求拦截器
request.interceptors.request(request => {
  let sign = uni.getStorageSync('maker_sign');
  if (sign && request.data.c != 'picker' && request.data.c != 'login') {
    request.data.maker_sign = sign;
  }

  return request;
});

// 响应拦截器
request.interceptors.response(response => {
  if (response.data.ret == -1 && response.config.c != 'login') {
    uni.removeStorageSync('maker_sign');
    uni.redirectTo({ url: '/pages/reg/login' });
  } else if (response.data.ret != 1) {
    if (!response.config?.noToast) {
      uni.showToast({
        title: response.data.msg || '系统繁忙，请稍后重试',
        icon: 'none'
      });
    }
  }

  return response.data;
});

// 设置默认配置
request.setConfig(requestConfig => {
  requestConfig.baseURL = config.siteApiUrl;

  return requestConfig;
});
export default request;
