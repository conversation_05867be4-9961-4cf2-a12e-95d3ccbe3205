import { computed } from 'vue';
import store from '../store/index';

const getSafeJson = str => {
  try {
    return JSON.parse(str || '{}');
  } catch (e) {
    return {};
  }
};

export default {
  install(app) {
    app.directive('power', {
      mounted(el, binding) {
        const powers = computed(() => {
          const vuexData = getSafeJson(uni.getStorageSync('vuex') || {});
          const powersStr = store.state?.loginInfo?.powers || vuexData?.loginInfo?.powers || '';
          return powersStr
            .split(',')
            .map(item => item.trim())
            .filter(Boolean);
        });

        if (powers.value) {
          const power = powers.value;
          const { value } = binding;
          if (!value) return;
          if (!power.includes(value)) {
            el.style.display = 'none';
          }
        }
      }
    });
  }
};
