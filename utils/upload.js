import systemConfig from '../config';


/**
 * 通用文件上传方法
 * @param {Object} options - 上传配置选项
 * @param {String} options.filePath - 文件路径
 * @param {String} options.module - 模块名称，默认为 'member'
 * @param {Number} options.thumb - 是否生成缩略图，默认为 1
 * @param {Number} options.forbidcom - 是否禁止压缩，默认为 0
 * @param {Number} options.forbidwater - 是否禁止水印，默认为 0
 * @returns {Promise} 返回上传结果的Promise
 */
export const uploadFile = (options) => {
  const {
    filePath,
    module = 'member',
    thumb = 1,
    forbidcom = 0,
    forbidwater = 0
  } = options;

  return new Promise((resolve, reject) => {
    uni.showLoading({ title: '上传中...' });

    uni.uploadFile({
      url: `${systemConfig.siteApiUrl}?m=crm&c=upload`,
      filePath: filePath,
      name: 'uploadpart',
      formData: {
        thumb,
        module,
        forbidcom,
        forbidwater,
        a: 'image',
        maker_sign: uni.getStorageSync('maker_sign')
      },
      success: uploadRes => {
        try {
          const resData = JSON.parse(uploadRes.data);
          if (resData.ret === 1) {
            uni.showToast({
              title: '上传成功', icon: 'none',
              image: '/assets/svg/success.svg'
            });
            resolve(resData.result);
          } else {
            uni.showToast({ title: resData.msg || '上传失败', icon: 'none' });
            reject(new Error(resData.msg || '上传失败'));
          }
        } catch (error) {
          uni.showToast({ title: '解析响应失败', icon: 'none' });
          reject(error);
        }
      },
      fail: err => {
        console.error('上传失败:', err);
        uni.showToast({ title: '上传失败', icon: 'none' });
        reject(err);
      },
      complete: () => {
        uni.hideLoading();
      }
    });
  });
};

/**
 * 选择并上传图片
 * @param {Object} options - 上传配置选项
 * @param {Number} options.count - 可选择的图片数量，默认为1
 * @param {String} options.module - 模块名称，默认为 'member'
 * @param {Number} options.thumb - 是否生成缩略图，默认为 1
 * @param {Number} options.forbidcom - 是否禁止压缩，默认为 0
 * @param {Number} options.forbidwater - 是否禁止水印，默认为 0
 * @returns {Promise} 返回上传结果的Promise
 */
export const chooseAndUploadImage = (options = {}) => {
  const {
    count = 1,
    module = 'member',
    thumb = 1,
    forbidcom = 0,
    forbidwater = 0
  } = options;

  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      success: chooseRes => {
        const filePath = chooseRes.tempFilePaths[0];
        uploadFile({
          filePath,
          module,
          thumb,
          forbidcom,
          forbidwater
        })
          .then(result => resolve(result))
          .catch(error => reject(error));
      },
      fail: err => {
        console.error('选择图片失败:', err);
        uni.showToast({ title: '选择图片失败', icon: 'none' });
        reject(err);
      }
    });
  });
};