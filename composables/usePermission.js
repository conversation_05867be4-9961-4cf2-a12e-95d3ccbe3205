import { computed } from 'vue'
import { useStore } from 'vuex'

export function usePermission() {
  const store = useStore()

  const getSafeJson = (str) => {
    try {
      return JSON.parse(str || '{}')
    } catch (e) {
      return {}
    }
  }

  // 获取权限和模块列表
  const mods = computed(() => {
    const vuexData = getSafeJson(uni.getStorageSync('vuex') || {})
    const modsStr = store.state?.loginInfo?.mods || vuexData?.loginInfo?.mods || ''
    return modsStr.split(',').map(item => item.trim()).filter(Boolean)
  })

  const powers = computed(() => {
    const vuexData = getSafeJson(uni.getStorageSync('vuex') || {})
    const powersStr = store.state?.loginInfo?.powers || vuexData?.loginInfo?.powers || ''
    return powersStr.split(',').map(item => item.trim()).filter(Boolean)
  })

  // 检查是否有权限
  const hasPermission = (mod, power) => {
    return mods.value.includes(mod) && powers.value.includes(power)
  }

  // 检查是否有任一权限
  const hasAnyPermission = (permissions) => {
    return permissions.some(([mod, power]) => hasPermission(mod, power))
  }

  // 检查mods是否有权限
  const hasModsPermission = (modsToCheck) => {
    return modsToCheck.some(mod => mods.value.includes(mod))
  }

  // 检查powers是否有权限
  const hasPowerPermission = (powersToCheck) => {
    return powersToCheck.some(power => powers.value.includes(power))
  }


  return {
    mods,
    powers,
    hasPermission,
    hasAnyPermission,
    hasModsPermission,
    hasPowerPermission,
    getSafeJson
  }
}