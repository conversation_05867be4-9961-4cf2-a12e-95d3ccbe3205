import { ref, computed, watch } from 'vue';
import { debounce } from '@/utils/hooks.js';
export function useAreaSearch(areas) {
  const searchKeyword = ref('');
  const searchResults = ref([]);
  const searchCache = new Map();

  // 预处理数据（使用计算属性确保响应式）
  const preprocessedAreas = computed(() => {
    return areas.value.map(province => ({
      ...province,
      textLower: province.text.toLowerCase(),
      children: province.children?.map(city => ({
        ...city,
        textLower: city.text.toLowerCase(),
        children: city.children?.map(district => ({
          ...district,
          textLower: district.text.toLowerCase()
        }))
      }))
    }));
  });

  // 创建搜索结果对象
  const createSearchResult = (item, path) => {
    const TEXTS = {
      1: `${path[0]}`,
      2: `${path[0]} / ${path[1]}`,
      3: `${path[0]} / ${path[1]} / ${path[2]}`
    }
    return {
      ...item,
      text: TEXTS[path.length],
      path
    };
  };

  const performSearch = (keyword) => {
    if (!keyword) {
      searchResults.value = [];
      return;
    }

    if (searchCache.has(keyword)) {
      searchResults.value = searchCache.get(keyword);
      return;
    }

    const results = [];
    const data = preprocessedAreas.value; // 使用预处理后的数据

    for (const province of data) {
      if (province.textLower.includes(keyword)) {
        results.push(createSearchResult(province, [province.text]));
      }

      if (province.children) {
        for (const city of province.children) {
          if (city.textLower.includes(keyword)) {
            results.push(createSearchResult(city, [province.text, city.text]));
          }

          if (city.children) {
            for (const district of city.children) {
              if (district.textLower.includes(keyword)) {
                results.push(createSearchResult(district, [province.text, city.text, district.text]));
              }
            }
          }
        }
      }
    }

    // 排序
    const sorted = results.sort((a, b) => {
      const aExact = a.path[a.path.length - 1].toLowerCase() === keyword;
      const bExact = b.path[b.path.length - 1].toLowerCase() === keyword;
      return (bExact - aExact) || (b.path.length - a.path.length);
    });

    searchCache.set(keyword, sorted);
    searchResults.value = sorted;
  };

  // 使用防抖的搜索函数
  const debouncedSearch = debounce(() => {
    performSearch(searchKeyword.value.trim().toLowerCase());
  }, 50);

  // 监听搜索关键词变化
  watch(searchKeyword, (newVal) => {
    if (!newVal.trim()) {
      searchResults.value = [];
      searchCache.clear();
    } else {
      debouncedSearch();
    }
  });

  const hasSearchResults = computed(() => {
    return searchKeyword.value && searchResults.value.length;
  });

  const emptyDescription = computed(() => {
    return searchKeyword.value ? '暂无搜索内容' : '暂无内容';
  });

  // 辅助函数：根据value值查找地区
  const findAreaByValue = value => {
    let province = null,
      city = null,
      district = null;

    // 遍历省级
    for (const p of areas.value) {
      if (p.value === value) {
        province = p;
        break;
      }

      // 遍历市级
      if (p.children) {
        for (const c of p.children) {
          if (c.value === value) {
            province = p;
            city = c;
            break;
          }

          // 遍历区级
          if (c.children) {
            for (const d of c.children) {
              if (d.value === value) {
                province = p;
                city = c;
                district = d;
                break;
              }
            }
          }
        }
      }
    }

    return { province, city, district };
  };

  return {
    searchKeyword,
    searchResults,
    hasSearchResults,
    emptyDescription,
    findAreaByValue
  };
}