<script setup>
import { ref } from 'vue';
import { useStore } from 'vuex';
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import oePicker from '@/utils/picker';
import http from '@/utils/api.js';

// #ifdef H5
let lastTouchEnd = 0;

// 阻止双指缩放
const handleTouchStart = event => {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
};

// 阻止双击缩放
const handleTouchEnd = event => {
  const now = Date.now();
  if (now - lastTouchEnd <= 300) {
    event.preventDefault();
  }
  lastTouchEnd = now;
};

// 阻止手势事件（部分 iOS 版本需要）
const handleGestureStart = event => {
  event.preventDefault();
};
// #endif

let config = {};
const store = useStore();
const init = isGetHC => {
  store.dispatch('getConfig', isGetHC).then(res => {
    if (res && res.ret == 1) {
      config = res.result;
      document.title = config.sitename;
      var link = document.querySelector("link[rel*='icon']");
      if (link) link.href = config.siteurl + 'favicon.ico';
    }
  });
};

const updateAreaPickerData = async (action, storageKey) => {
  const res = await http.post('', { m: 'vuewap', c: 'picker', a: action });
  if (res.ret === 1) {
    uni.setStorageSync(storageKey, JSON.stringify(res.result));
  }
  return res;
};

const updateMakerdata = () => {
  http.post('', { m: 'crm', c: 'ajax', a: 'twomaker', viewtype: 3 }).then(res => {
    if (res.ret == 1) {
      uni.setStorageSync('makerdata', JSON.stringify(res.result.data));
    }
  });
};

const editConfigTime = () => {
  // 用户信息
  store.dispatch('getUserInfo');

  // picker更新时间戳
  const timeParams = { m: 'vuewap', c: 'picker', a: 'cfgtime' };
  http
    .post('', timeParams)
    .then(res => {
      if (res.ret == 1) {
        let isCtime = uni.getStorageSync('newConfigTime') || '';

        init(isCtime == res.result);
        if (isCtime != res.result) {
          oePicker();
          updateMakerdata();
        }
        uni.setStorageSync('newConfigTime', res.result);
      } else {
        init();
      }
    })
    .catch(() => {
      init();
    });

  // 录入红娘数据源
  if (!uni.getStorageSync('makerdata')) {
    updateMakerdata();
  }

  // 地区数据源
  if (!uni.getStorageSync('district')) {
    updateAreaPickerData('area', 'district');
  }

  if (!uni.getStorageSync('hometown')) {
    updateAreaPickerData('hometown', 'hometown');
  }
};
editConfigTime();

onLaunch(() => {
  const store = useStore();

  // 防止重复跳转
  let isChecking = false;
  const checkLogin = () => {
    if (isChecking) return;
    isChecking = true;

    const sign = uni.getStorageSync('maker_sign');
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1] || {};
    const currentRoute = currentPage.route;

    if (!sign) {
      if (currentRoute !== 'pages/reg/login') {
        uni.reLaunch({ url: '/pages/reg/login' });
      }
    } else if (!store.state.loginInfo.makerid) {
      store
        .dispatch('getUserInfo')
        .then(res => {
          if (res.ret !== 1) {
            uni.removeStorageSync('maker_sign');
            uni.reLaunch({ url: '/pages/reg/login' });
          }
        })
        .finally(() => (isChecking = false));
    } else {
      isChecking = false;
    }
  };

  setTimeout(checkLogin, 50);

  // #ifdef H5
  document.addEventListener('touchstart', handleTouchStart);
  document.addEventListener('touchend', handleTouchEnd);
  document.addEventListener('gesturestart', handleGestureStart);
  // #endif
});

onShow(() => {
  console.log('App Sho11w');

  // #ifdef MP-WEIXIN
  uni.hideTabBar({
    animation: false,
    success: () => {},
    fail: () => {}
  });
  //#endif
});

onHide(() => {
  console.log('App Hide');

  // #ifdef H5
  document.removeEventListener('touchstart', handleTouchStart);
  document.removeEventListener('touchend', handleTouchEnd);
  document.removeEventListener('gesturestart', handleGestureStart);
  // #endif
});
</script>

<style lang="scss">
@import '~@/style/default.scss';
@import '~@/style/comm.scss';
@import url('~@/assets/iconfont/iconfont.css');

#app {
  height: 100%;
  overflow: auto;
}

::v-deep .uni-tabbar-bottom {
  opacity: 0;
}

// 强制覆盖uni.showToast样式
::v-deep uni-toast {
  z-index: 9999;
}

::v-deep .uni-toast {
  width: inherit;
  min-width: 5em;
  max-width: 10em;
  padding: 32rpx;
  border-radius: 32rpx;
  z-index: 999;

  .uni-toast__icon {
    margin-top: 0;
  }

  .uni-toast__content {
    margin-top: 8rpx;
    margin-bottom: 0;
  }
}
</style>
